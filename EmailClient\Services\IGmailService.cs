using EmailClient.Models;

namespace EmailClient.Services;

public interface IGmailService
{
    /// <summary>
    /// Tests if Gmail API connection is working with current OAuth credentials
    /// </summary>
    /// <returns>True if connection is successful</returns>
    Task<bool> TestConnectionAsync();
    
    /// <summary>
    /// Gets Gmail folders (labels) for the authenticated user
    /// </summary>
    /// <returns>Collection of email folders</returns>
    Task<IEnumerable<EmailFolder>> GetFoldersAsync();
    
    /// <summary>
    /// Gets messages from a specific Gmail folder/label
    /// </summary>
    /// <param name="folder">The folder to get messages from</param>
    /// <param name="limit">Maximum number of messages to retrieve</param>
    /// <returns>Collection of email messages</returns>
    Task<IEnumerable<EmailMessage>> GetMessagesAsync(EmailFolder folder, int limit = 50);
    
    /// <summary>
    /// Gets detailed information for a specific message
    /// </summary>
    /// <param name="messageId">Gmail message ID</param>
    /// <returns>Detailed email message</returns>
    Task<EmailMessage> GetMessageDetailsAsync(string messageId);
    
    /// <summary>
    /// Marks a message as read
    /// </summary>
    /// <param name="messageId">Gmail message ID</param>
    Task MarkAsReadAsync(string messageId);
    
    /// <summary>
    /// Marks a message as unread
    /// </summary>
    /// <param name="messageId">Gmail message ID</param>
    Task MarkAsUnreadAsync(string messageId);
    
    /// <summary>
    /// Deletes a message (moves to trash)
    /// </summary>
    /// <param name="messageId">Gmail message ID</param>
    Task DeleteMessageAsync(string messageId);

    /// <summary>
    /// Moves a message to a different label/folder
    /// </summary>
    /// <param name="messageId">Gmail message ID</param>
    /// <param name="sourceLabelId">Source label ID</param>
    /// <param name="targetLabelId">Target label ID</param>
    Task MoveMessageAsync(string messageId, string sourceLabelId, string targetLabelId);

    /// <summary>
    /// Searches for messages using Gmail search syntax
    /// </summary>
    /// <param name="query">Gmail search query</param>
    /// <param name="limit">Maximum number of results</param>
    /// <returns>Collection of matching messages</returns>
    Task<IEnumerable<EmailMessage>> SearchMessagesAsync(string query, int limit = 100);
    
    /// <summary>
    /// Gets the authenticated user's email address
    /// </summary>
    /// <returns>User's email address</returns>
    Task<string> GetUserEmailAsync();
}
