using Microsoft.Extensions.Configuration;
using System.Text.Json;
using System.IO;

namespace EmailClient.Services;

public class SettingsService : ISettingsService
{
    private readonly Dictionary<string, object> _settings = new();
    private readonly string _settingsFile = "appsettings.json";

    public SettingsService()
    {
        LoadSettings();
    }

    private void LoadSettings()
    {
        try
        {
            if (File.Exists(_settingsFile))
            {
                var json = File.ReadAllText(_settingsFile);
                var settings = JsonSerializer.Deserialize<Dictionary<string, object>>(json);
                if (settings != null)
                {
                    foreach (var kvp in settings)
                    {
                        _settings[kvp.Key] = kvp.Value;
                    }
                }
            }
        }
        catch
        {
            // If settings file is corrupted, start with empty settings
        }
    }

    private async Task SaveSettings()
    {
        try
        {
            var json = JsonSerializer.Serialize(_settings, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(_settingsFile, json);
        }
        catch
        {
            // Handle save errors silently for now
        }
    }

    public Task<T?> GetSettingAsync<T>(string key)
    {
        if (_settings.TryGetValue(key, out var value))
        {
            try
            {
                if (value is JsonElement element)
                {
                    return Task.FromResult(JsonSerializer.Deserialize<T>(element.GetRawText()));
                }
                return Task.FromResult((T)value);
            }
            catch
            {
                return Task.FromResult(default(T));
            }
        }
        return Task.FromResult(default(T));
    }

    public async Task SetSettingAsync<T>(string key, T value)
    {
        _settings[key] = value!;
        await SaveSettings();
    }

    public async Task<bool> GetBoolSettingAsync(string key, bool defaultValue = false)
    {
        var value = await GetSettingAsync<bool?>(key);
        return value ?? defaultValue;
    }

    public async Task<int> GetIntSettingAsync(string key, int defaultValue = 0)
    {
        var value = await GetSettingAsync<int?>(key);
        return value ?? defaultValue;
    }

    public async Task<string> GetStringSettingAsync(string key, string defaultValue = "")
    {
        var value = await GetSettingAsync<string>(key);
        return value ?? defaultValue;
    }

    public async Task SetBoolSettingAsync(string key, bool value)
    {
        await SetSettingAsync(key, value);
    }

    public async Task SetIntSettingAsync(string key, int value)
    {
        await SetSettingAsync(key, value);
    }

    public async Task SetStringSettingAsync(string key, string value)
    {
        await SetSettingAsync(key, value);
    }
}
