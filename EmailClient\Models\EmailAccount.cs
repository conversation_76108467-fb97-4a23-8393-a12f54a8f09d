using System.ComponentModel.DataAnnotations;

namespace EmailClient.Models;

public enum AccountType
{
    IMAP = 0,
    OAuth = 1
}

public class EmailAccount
{
    public int Id { get; set; }

    [Required]
    public string Name { get; set; } = string.Empty;

    [Required]
    [EmailAddress]
    public string EmailAddress { get; set; } = string.Empty;

    // Account type (IMAP or OAuth)
    public AccountType AccountType { get; set; } = AccountType.IMAP;

    // IMAP-specific properties (only used when AccountType = IMAP)
    public string ImapServer { get; set; } = string.Empty;

    public int ImapPort { get; set; } = 993;

    public bool UseSsl { get; set; } = true;

    public string Username { get; set; } = string.Empty;

    public string Password { get; set; } = string.Empty; // In production, this should be encrypted

    // OAuth-specific properties (only used when AccountType = OAuth)
    public string OAuthProvider { get; set; } = string.Empty; // e.g., "Google"

    public bool IsEnabled { get; set; } = true;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime LastSyncAt { get; set; }

    // Helper properties
    public bool IsOAuthAccount => AccountType == AccountType.OAuth;
    public bool IsImapAccount => AccountType == AccountType.IMAP;

    // Navigation properties
    public virtual ICollection<EmailMessage> Messages { get; set; } = new List<EmailMessage>();
    public virtual ICollection<EmailFolder> Folders { get; set; } = new List<EmailFolder>();
}
