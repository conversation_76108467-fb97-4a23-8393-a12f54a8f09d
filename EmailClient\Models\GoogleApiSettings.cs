using System.ComponentModel.DataAnnotations;

namespace EmailClient.Models;

public class GoogleApiSettings
{
    public int Id { get; set; }
    
    [Required]
    public string ClientId { get; set; } = string.Empty;
    
    [Required]
    public string ClientSecret { get; set; } = string.Empty; // Should be encrypted in production
    
    public string AccessToken { get; set; } = string.Empty; // Should be encrypted in production
    
    public string RefreshToken { get; set; } = string.Empty; // Should be encrypted in production
    
    public DateTime? TokenExpiresAt { get; set; }
    
    public string Scopes { get; set; } = string.Empty; // JSON array of granted scopes
    
    public bool ContactsSyncEnabled { get; set; } = true;
    
    public bool CalendarSyncEnabled { get; set; } = true;
    
    public int ContactsSyncIntervalMinutes { get; set; } = 60; // Default 1 hour
    
    public int CalendarSyncIntervalMinutes { get; set; } = 30; // Default 30 minutes
    
    public DateTime? LastContactsSync { get; set; }
    
    public DateTime? LastCalendarSync { get; set; }
    
    public string ContactsSyncToken { get; set; } = string.Empty; // For incremental sync
    
    public string CalendarSyncToken { get; set; } = string.Empty; // For incremental sync
    
    public bool IsAuthenticated { get; set; } = false;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}

// Helper class for OAuth scopes
public static class GoogleScopes
{
    public const string ContactsReadOnly = "https://www.googleapis.com/auth/contacts.readonly";
    public const string Contacts = "https://www.googleapis.com/auth/contacts";
    public const string CalendarReadOnly = "https://www.googleapis.com/auth/calendar.readonly";
    public const string Calendar = "https://www.googleapis.com/auth/calendar";
    public const string CalendarEvents = "https://www.googleapis.com/auth/calendar.events";
    public const string Profile = "https://www.googleapis.com/auth/userinfo.profile";
    public const string Email = "https://www.googleapis.com/auth/userinfo.email";

    // Gmail scopes
    public const string GmailReadOnly = "https://www.googleapis.com/auth/gmail.readonly";
    public const string Gmail = "https://www.googleapis.com/auth/gmail.modify";
    public const string GmailCompose = "https://www.googleapis.com/auth/gmail.compose";
    public const string GmailSend = "https://www.googleapis.com/auth/gmail.send";
    public const string GmailLabels = "https://www.googleapis.com/auth/gmail.labels";

    public static readonly string[] DefaultScopes = new[]
    {
        ContactsReadOnly,
        CalendarReadOnly,
        Profile,
        Email
    };

    public static readonly string[] FullAccessScopes = new[]
    {
        Contacts,
        Calendar,
        Profile,
        Email
    };

    public static readonly string[] GmailScopes = new[]
    {
        ContactsReadOnly,
        CalendarReadOnly,
        Profile,
        Email,
        GmailReadOnly,
        GmailCompose,
        GmailSend,
        GmailLabels
    };
}
