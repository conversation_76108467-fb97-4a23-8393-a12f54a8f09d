using EmailClient.Models;
using Microsoft.Extensions.Logging;

namespace EmailClient.Services;

public class UnifiedEmailService : IUnifiedEmailService
{
    private readonly IImapService _imapService;
    private readonly IGmailService _gmailService;
    private readonly ILogger<UnifiedEmailService> _logger;

    public UnifiedEmailService(
        IImapService imapService, 
        IGmailService gmailService, 
        ILogger<UnifiedEmailService> logger)
    {
        _imapService = imapService;
        _gmailService = gmailService;
        _logger = logger;
    }

    public async Task<bool> TestConnectionAsync(EmailAccount account)
    {
        try
        {
            if (account.IsOAuthAccount)
            {
                if (account.OAuthProvider.Equals("Google", StringComparison.OrdinalIgnoreCase))
                {
                    return await _gmailService.TestConnectionAsync();
                }
                else
                {
                    _logger.LogWarning("Unsupported OAuth provider: {Provider}", account.OAuthProvider);
                    return false;
                }
            }
            else
            {
                return await _imapService.TestConnectionAsync(account);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to test connection for account {Email}", account.EmailAddress);
            return false;
        }
    }

    public async Task<IEnumerable<EmailFolder>> GetFoldersAsync(EmailAccount account)
    {
        try
        {
            if (account.IsOAuthAccount)
            {
                if (account.OAuthProvider.Equals("Google", StringComparison.OrdinalIgnoreCase))
                {
                    var folders = await _gmailService.GetFoldersAsync();
                    // Set the account ID for each folder
                    foreach (var folder in folders)
                    {
                        folder.AccountId = account.Id;
                    }
                    return folders;
                }
                else
                {
                    _logger.LogWarning("Unsupported OAuth provider: {Provider}", account.OAuthProvider);
                    return new List<EmailFolder>();
                }
            }
            else
            {
                return await _imapService.GetFoldersAsync(account);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get folders for account {Email}", account.EmailAddress);
            return new List<EmailFolder>();
        }
    }

    public async Task<IEnumerable<EmailMessage>> GetMessagesAsync(EmailAccount account, EmailFolder folder, int limit = 50)
    {
        try
        {
            if (account.IsOAuthAccount)
            {
                if (account.OAuthProvider.Equals("Google", StringComparison.OrdinalIgnoreCase))
                {
                    var messages = await _gmailService.GetMessagesAsync(folder, limit);
                    // Set the account ID and folder ID for each message
                    foreach (var message in messages)
                    {
                        message.AccountId = account.Id;
                        message.FolderId = folder.Id;
                    }
                    return messages;
                }
                else
                {
                    _logger.LogWarning("Unsupported OAuth provider: {Provider}", account.OAuthProvider);
                    return new List<EmailMessage>();
                }
            }
            else
            {
                return await _imapService.GetMessagesAsync(account, folder, limit);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get messages for account {Email}, folder {Folder}", 
                account.EmailAddress, folder.Name);
            return new List<EmailMessage>();
        }
    }

    public async Task<EmailMessage> GetMessageDetailsAsync(EmailAccount account, EmailFolder folder, string messageId)
    {
        try
        {
            if (account.IsOAuthAccount)
            {
                if (account.OAuthProvider.Equals("Google", StringComparison.OrdinalIgnoreCase))
                {
                    var message = await _gmailService.GetMessageDetailsAsync(messageId);
                    message.AccountId = account.Id;
                    message.FolderId = folder.Id;
                    return message;
                }
                else
                {
                    throw new NotSupportedException($"Unsupported OAuth provider: {account.OAuthProvider}");
                }
            }
            else
            {
                return await _imapService.GetMessageDetailsAsync(account, folder, messageId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get message details for account {Email}, message {MessageId}", 
                account.EmailAddress, messageId);
            throw;
        }
    }

    public async Task MarkAsReadAsync(EmailAccount account, EmailFolder folder, string messageId)
    {
        try
        {
            if (account.IsOAuthAccount)
            {
                if (account.OAuthProvider.Equals("Google", StringComparison.OrdinalIgnoreCase))
                {
                    await _gmailService.MarkAsReadAsync(messageId);
                }
                else
                {
                    throw new NotSupportedException($"Unsupported OAuth provider: {account.OAuthProvider}");
                }
            }
            else
            {
                await _imapService.MarkAsReadAsync(account, folder, messageId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to mark message as read for account {Email}, message {MessageId}", 
                account.EmailAddress, messageId);
            throw;
        }
    }

    public async Task MarkAsUnreadAsync(EmailAccount account, EmailFolder folder, string messageId)
    {
        try
        {
            if (account.IsOAuthAccount)
            {
                if (account.OAuthProvider.Equals("Google", StringComparison.OrdinalIgnoreCase))
                {
                    await _gmailService.MarkAsUnreadAsync(messageId);
                }
                else
                {
                    throw new NotSupportedException($"Unsupported OAuth provider: {account.OAuthProvider}");
                }
            }
            else
            {
                await _imapService.MarkAsUnreadAsync(account, folder, messageId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to mark message as unread for account {Email}, message {MessageId}", 
                account.EmailAddress, messageId);
            throw;
        }
    }

    public async Task DeleteMessageAsync(EmailAccount account, EmailFolder folder, string messageId)
    {
        try
        {
            if (account.IsOAuthAccount)
            {
                if (account.OAuthProvider.Equals("Google", StringComparison.OrdinalIgnoreCase))
                {
                    await _gmailService.DeleteMessageAsync(messageId);
                }
                else
                {
                    throw new NotSupportedException($"Unsupported OAuth provider: {account.OAuthProvider}");
                }
            }
            else
            {
                await _imapService.DeleteMessageAsync(account, folder, messageId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete message for account {Email}, message {MessageId}",
                account.EmailAddress, messageId);
            throw;
        }
    }

    public async Task MoveMessageAsync(EmailAccount account, EmailFolder sourceFolder, EmailFolder targetFolder, string messageId)
    {
        try
        {
            if (account.IsOAuthAccount)
            {
                if (account.OAuthProvider.Equals("Google", StringComparison.OrdinalIgnoreCase))
                {
                    await _gmailService.MoveMessageAsync(messageId, sourceFolder.FullName, targetFolder.FullName);
                }
                else
                {
                    throw new NotSupportedException($"Unsupported OAuth provider: {account.OAuthProvider}");
                }
            }
            else
            {
                await _imapService.MoveMessageAsync(account, sourceFolder, targetFolder, messageId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to move message for account {Email}, message {MessageId}",
                account.EmailAddress, messageId);
            throw;
        }
    }

    public async Task<IEnumerable<EmailMessage>> SearchMessagesAsync(EmailAccount account, string query, int limit = 100)
    {
        try
        {
            if (account.IsOAuthAccount)
            {
                if (account.OAuthProvider.Equals("Google", StringComparison.OrdinalIgnoreCase))
                {
                    var messages = await _gmailService.SearchMessagesAsync(query, limit);
                    // Set the account ID for each message
                    foreach (var message in messages)
                    {
                        message.AccountId = account.Id;
                    }
                    return messages;
                }
                else
                {
                    _logger.LogWarning("Unsupported OAuth provider: {Provider}", account.OAuthProvider);
                    return new List<EmailMessage>();
                }
            }
            else
            {
                // For IMAP, we need to search in a specific folder. Let's search in the Inbox by default.
                var folders = await _imapService.GetFoldersAsync(account);
                var inboxFolder = folders.FirstOrDefault(f => f.Type == FolderType.Inbox);
                if (inboxFolder != null)
                {
                    return await _imapService.SearchMessagesAsync(account, inboxFolder, query);
                }
                else
                {
                    _logger.LogWarning("No inbox folder found for IMAP account {Email}", account.EmailAddress);
                    return new List<EmailMessage>();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search messages for account {Email}, query {Query}", 
                account.EmailAddress, query);
            return new List<EmailMessage>();
        }
    }
}
