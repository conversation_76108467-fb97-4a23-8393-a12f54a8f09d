using EmailClient.Models;

namespace EmailClient.Services;

/// <summary>
/// Unified email service that can work with both IMAP and OAuth-based accounts
/// </summary>
public interface IUnifiedEmailService
{
    /// <summary>
    /// Tests connection for any account type (IMAP or OAuth)
    /// </summary>
    /// <param name="account">Email account to test</param>
    /// <returns>True if connection is successful</returns>
    Task<bool> TestConnectionAsync(EmailAccount account);
    
    /// <summary>
    /// Gets folders for any account type
    /// </summary>
    /// <param name="account">Email account</param>
    /// <returns>Collection of email folders</returns>
    Task<IEnumerable<EmailFolder>> GetFoldersAsync(EmailAccount account);
    
    /// <summary>
    /// Gets messages from a specific folder for any account type
    /// </summary>
    /// <param name="account">Email account</param>
    /// <param name="folder">Folder to get messages from</param>
    /// <param name="limit">Maximum number of messages to retrieve</param>
    /// <returns>Collection of email messages</returns>
    Task<IEnumerable<EmailMessage>> GetMessagesAsync(EmailAccount account, EmailFolder folder, int limit = 50);
    
    /// <summary>
    /// Gets detailed information for a specific message
    /// </summary>
    /// <param name="account">Email account</param>
    /// <param name="folder">Folder containing the message</param>
    /// <param name="messageId">Message ID</param>
    /// <returns>Detailed email message</returns>
    Task<EmailMessage> GetMessageDetailsAsync(EmailAccount account, EmailFolder folder, string messageId);
    
    /// <summary>
    /// Marks a message as read
    /// </summary>
    /// <param name="account">Email account</param>
    /// <param name="folder">Folder containing the message</param>
    /// <param name="messageId">Message ID</param>
    Task MarkAsReadAsync(EmailAccount account, EmailFolder folder, string messageId);
    
    /// <summary>
    /// Marks a message as unread
    /// </summary>
    /// <param name="account">Email account</param>
    /// <param name="folder">Folder containing the message</param>
    /// <param name="messageId">Message ID</param>
    Task MarkAsUnreadAsync(EmailAccount account, EmailFolder folder, string messageId);
    
    /// <summary>
    /// Deletes a message
    /// </summary>
    /// <param name="account">Email account</param>
    /// <param name="folder">Folder containing the message</param>
    /// <param name="messageId">Message ID</param>
    Task DeleteMessageAsync(EmailAccount account, EmailFolder folder, string messageId);
    
    /// <summary>
    /// Moves a message to a different folder
    /// </summary>
    /// <param name="account">Email account</param>
    /// <param name="sourceFolder">Source folder</param>
    /// <param name="targetFolder">Target folder</param>
    /// <param name="messageId">Message ID</param>
    Task MoveMessageAsync(EmailAccount account, EmailFolder sourceFolder, EmailFolder targetFolder, string messageId);

    /// <summary>
    /// Searches for messages
    /// </summary>
    /// <param name="account">Email account</param>
    /// <param name="query">Search query</param>
    /// <param name="limit">Maximum number of results</param>
    /// <returns>Collection of matching messages</returns>
    Task<IEnumerable<EmailMessage>> SearchMessagesAsync(EmailAccount account, string query, int limit = 100);
}
