using System.ComponentModel.DataAnnotations;

namespace EmailClient.Models;

public class EmailMessage
{
    public int Id { get; set; }
    
    [Required]
    public string MessageId { get; set; } = string.Empty; // IMAP UID or Message-ID

    public string ThreadId { get; set; } = string.Empty; // Gmail thread ID

    public int AccountId { get; set; }
    public virtual EmailAccount Account { get; set; } = null!;
    
    public int? FolderId { get; set; }
    public virtual EmailFolder? Folder { get; set; }
    
    [Required]
    public string Subject { get; set; } = string.Empty;
    
    [Required]
    public string FromAddress { get; set; } = string.Empty;
    
    public string FromName { get; set; } = string.Empty;
    
    [Required]
    public string ToAddresses { get; set; } = string.Empty; // JSON array of addresses
    
    public string CcAddresses { get; set; } = string.Empty; // JSON array of addresses
    
    public string BccAddresses { get; set; } = string.Empty; // JSON array of addresses
    
    public DateTime DateSent { get; set; }
    
    public DateTime DateReceived { get; set; } = DateTime.UtcNow;
    
    public string TextBody { get; set; } = string.Empty;
    
    public string HtmlBody { get; set; } = string.Empty;
    
    public bool IsRead { get; set; } = false;
    
    public bool IsFlagged { get; set; } = false;
    
    public bool IsDeleted { get; set; } = false;
    
    public int Size { get; set; } // Size in bytes
    
    public bool HasAttachments { get; set; } = false;
    
    // Navigation properties
    public virtual ICollection<EmailAttachment> Attachments { get; set; } = new List<EmailAttachment>();
}
