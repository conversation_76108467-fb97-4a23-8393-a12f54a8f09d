using Google.Apis.Gmail.v1;
using Google.Apis.Gmail.v1.Data;
using Google.Apis.Services;
using Microsoft.Extensions.Logging;
using EmailClient.Models;
using System.Text;
using MimeKit;

namespace EmailClient.Services;

public class GmailService : IGmailService
{
    private readonly IGoogleAuthService _googleAuthService;
    private readonly ILogger<GmailService> _logger;
    private Google.Apis.Gmail.v1.GmailService? _gmailApiService;

    public GmailService(IGoogleAuthService googleAuthService, ILogger<GmailService> logger)
    {
        _googleAuthService = googleAuthService;
        _logger = logger;
    }

    private async Task<Google.Apis.Gmail.v1.GmailService> GetGmailServiceAsync()
    {
        if (_gmailApiService != null)
            return _gmailApiService;

        var credential = await _googleAuthService.GetCredentialAsync();
        if (credential == null)
        {
            throw new InvalidOperationException("Google OAuth credential not available. Please authenticate first.");
        }

        _gmailApiService = new Google.Apis.Gmail.v1.GmailService(new BaseClientService.Initializer()
        {
            HttpClientInitializer = credential,
            ApplicationName = "Email Client"
        });

        return _gmailApiService;
    }

    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            var service = await GetGmailServiceAsync();
            var profile = await service.Users.GetProfile("me").ExecuteAsync();
            _logger.LogInformation("Gmail connection test successful for user: {Email}", profile.EmailAddress);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Gmail connection test failed");
            return false;
        }
    }

    public async Task<string> GetUserEmailAsync()
    {
        try
        {
            var service = await GetGmailServiceAsync();
            var profile = await service.Users.GetProfile("me").ExecuteAsync();
            return profile.EmailAddress ?? string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get user email from Gmail");
            return string.Empty;
        }
    }

    public async Task<IEnumerable<EmailFolder>> GetFoldersAsync()
    {
        try
        {
            var service = await GetGmailServiceAsync();
            var labelsRequest = service.Users.Labels.List("me");
            var labelsResponse = await labelsRequest.ExecuteAsync();

            var folders = new List<EmailFolder>();

            foreach (var label in labelsResponse.Labels)
            {
                var folder = new EmailFolder
                {
                    Name = label.Name,
                    FullName = label.Id,
                    Type = GetFolderType(label.Name, label.Type),
                    TotalCount = 0, // Gmail doesn't provide this directly
                    UnreadCount = 0   // Gmail doesn't provide this directly
                };

                folders.Add(folder);
            }

            return folders.OrderBy(f => GetFolderSortOrder(f.Type)).ThenBy(f => f.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get Gmail folders");
            return new List<EmailFolder>();
        }
    }

    private static FolderType GetFolderType(string labelName, string labelType)
    {
        return labelName?.ToLowerInvariant() switch
        {
            "inbox" => FolderType.Inbox,
            "sent" => FolderType.Sent,
            "drafts" => FolderType.Drafts,
            "trash" => FolderType.Trash,
            "spam" => FolderType.Spam,
            _ when labelType == "system" => FolderType.Archive,
            _ => FolderType.Custom
        };
    }

    private static int GetFolderSortOrder(FolderType type)
    {
        return type switch
        {
            FolderType.Inbox => 1,
            FolderType.Sent => 2,
            FolderType.Drafts => 3,
            FolderType.Trash => 4,
            FolderType.Spam => 5,
            FolderType.Archive => 6,
            FolderType.Custom => 7,
            _ => 8
        };
    }

    public async Task<IEnumerable<EmailMessage>> GetMessagesAsync(EmailFolder folder, int limit = 50)
    {
        try
        {
            var service = await GetGmailServiceAsync();
            
            // Get message list
            var listRequest = service.Users.Messages.List("me");
            listRequest.LabelIds = new[] { folder.FullName };
            listRequest.MaxResults = limit;
            
            var listResponse = await listRequest.ExecuteAsync();
            
            if (listResponse.Messages == null)
                return new List<EmailMessage>();

            var messages = new List<EmailMessage>();
            
            // Get detailed information for each message
            foreach (var messageRef in listResponse.Messages.Take(limit))
            {
                try
                {
                    var message = await GetMessageDetailsAsync(messageRef.Id);
                    messages.Add(message);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to get details for Gmail message {MessageId}", messageRef.Id);
                }
            }

            return messages.OrderByDescending(m => m.DateReceived);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get Gmail messages for folder {FolderName}", folder.Name);
            return new List<EmailMessage>();
        }
    }

    public async Task<EmailMessage> GetMessageDetailsAsync(string messageId)
    {
        try
        {
            var service = await GetGmailServiceAsync();
            var getRequest = service.Users.Messages.Get("me", messageId);
            getRequest.Format = UsersResource.MessagesResource.GetRequest.FormatEnum.Full;
            
            var gmailMessage = await getRequest.ExecuteAsync();
            
            return ConvertGmailMessageToEmailMessage(gmailMessage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get Gmail message details for {MessageId}", messageId);
            throw;
        }
    }

    private EmailMessage ConvertGmailMessageToEmailMessage(Message gmailMessage)
    {
        var emailMessage = new EmailMessage
        {
            MessageId = gmailMessage.Id,
            ThreadId = gmailMessage.ThreadId,
            Size = (int)(gmailMessage.SizeEstimate ?? 0)
        };

        // Parse headers
        if (gmailMessage.Payload?.Headers != null)
        {
            foreach (var header in gmailMessage.Payload.Headers)
            {
                switch (header.Name?.ToLowerInvariant())
                {
                    case "subject":
                        emailMessage.Subject = header.Value ?? string.Empty;
                        break;
                    case "from":
                        ParseFromHeader(header.Value, emailMessage);
                        break;
                    case "to":
                        emailMessage.ToAddresses = header.Value ?? string.Empty;
                        break;
                    case "date":
                        if (DateTime.TryParse(header.Value, out var date))
                            emailMessage.DateReceived = date;
                        break;
                }
            }
        }

        // Parse body
        if (gmailMessage.Payload != null)
        {
            var (textBody, htmlBody) = ExtractMessageBody(gmailMessage.Payload);
            emailMessage.TextBody = textBody;
            emailMessage.HtmlBody = htmlBody;
        }

        // Parse labels to determine read status
        if (gmailMessage.LabelIds != null)
        {
            emailMessage.IsRead = !gmailMessage.LabelIds.Contains("UNREAD");
            emailMessage.IsFlagged = gmailMessage.LabelIds.Contains("STARRED");
        }

        return emailMessage;
    }

    private static void ParseFromHeader(string? fromHeader, EmailMessage emailMessage)
    {
        if (string.IsNullOrEmpty(fromHeader))
            return;

        try
        {
            var mailboxAddress = MailboxAddress.Parse(fromHeader);
            emailMessage.FromName = mailboxAddress.Name ?? string.Empty;
            emailMessage.FromAddress = mailboxAddress.Address ?? string.Empty;
        }
        catch
        {
            // Fallback to simple parsing
            emailMessage.FromAddress = fromHeader;
            emailMessage.FromName = string.Empty;
        }
    }

    private static (string textBody, string htmlBody) ExtractMessageBody(Google.Apis.Gmail.v1.Data.MessagePart payload)
    {
        var textBody = string.Empty;
        var htmlBody = string.Empty;

        if (payload.Body?.Data != null)
        {
            var bodyData = Convert.FromBase64String(payload.Body.Data.Replace('-', '+').Replace('_', '/'));
            var bodyText = Encoding.UTF8.GetString(bodyData);
            
            if (payload.MimeType == "text/plain")
                textBody = bodyText;
            else if (payload.MimeType == "text/html")
                htmlBody = bodyText;
        }

        if (payload.Parts != null)
        {
            foreach (var part in payload.Parts)
            {
                var (partText, partHtml) = ExtractMessageBody(part);
                if (!string.IsNullOrEmpty(partText))
                    textBody = partText;
                if (!string.IsNullOrEmpty(partHtml))
                    htmlBody = partHtml;
            }
        }

        return (textBody, htmlBody);
    }

    public async Task MarkAsReadAsync(string messageId)
    {
        try
        {
            var service = await GetGmailServiceAsync();
            var modifyRequest = new ModifyMessageRequest
            {
                RemoveLabelIds = new[] { "UNREAD" }
            };
            
            await service.Users.Messages.Modify(modifyRequest, "me", messageId).ExecuteAsync();
            _logger.LogDebug("Marked Gmail message {MessageId} as read", messageId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to mark Gmail message {MessageId} as read", messageId);
            throw;
        }
    }

    public async Task MarkAsUnreadAsync(string messageId)
    {
        try
        {
            var service = await GetGmailServiceAsync();
            var modifyRequest = new ModifyMessageRequest
            {
                AddLabelIds = new[] { "UNREAD" }
            };
            
            await service.Users.Messages.Modify(modifyRequest, "me", messageId).ExecuteAsync();
            _logger.LogDebug("Marked Gmail message {MessageId} as unread", messageId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to mark Gmail message {MessageId} as unread", messageId);
            throw;
        }
    }

    public async Task DeleteMessageAsync(string messageId)
    {
        try
        {
            var service = await GetGmailServiceAsync();
            await service.Users.Messages.Trash("me", messageId).ExecuteAsync();
            _logger.LogDebug("Moved Gmail message {MessageId} to trash", messageId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete Gmail message {MessageId}", messageId);
            throw;
        }
    }

    public async Task<IEnumerable<EmailMessage>> SearchMessagesAsync(string query, int limit = 100)
    {
        try
        {
            var service = await GetGmailServiceAsync();
            
            var listRequest = service.Users.Messages.List("me");
            listRequest.Q = query;
            listRequest.MaxResults = limit;
            
            var listResponse = await listRequest.ExecuteAsync();
            
            if (listResponse.Messages == null)
                return new List<EmailMessage>();

            var messages = new List<EmailMessage>();
            
            foreach (var messageRef in listResponse.Messages.Take(limit))
            {
                try
                {
                    var message = await GetMessageDetailsAsync(messageRef.Id);
                    messages.Add(message);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to get details for search result message {MessageId}", messageRef.Id);
                }
            }

            return messages.OrderByDescending(m => m.DateReceived);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search Gmail messages with query: {Query}", query);
            return new List<EmailMessage>();
        }
    }
}
