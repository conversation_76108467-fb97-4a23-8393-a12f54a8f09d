using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using EmailClient.Data;
using EmailClient.Services;
using EmailClient.ViewModels;
using EmailClient.Views;
using Serilog;
using System.IO;
using System.Windows;

namespace EmailClient;

public partial class App : Application
{
    private IHost? _host;

    protected override void OnStartup(StartupEventArgs e)
    {
        // Set up global exception handling
        AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
        DispatcherUnhandledException += OnDispatcherUnhandledException;
        TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;

        try
        {
            // Configure Serilog with absolute path
            var logsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
            Directory.CreateDirectory(logsDirectory); // Ensure logs directory exists
            var logFilePath = Path.Combine(logsDirectory, "emailclient-.txt");

            Log.Logger = new LoggerConfiguration()
                .WriteTo.File(logFilePath,
                    rollingInterval: RollingInterval.Day,
                    shared: true) // Allow shared access for log viewer
                .CreateLogger();

            Log.Information("Application starting. Logs directory: {LogsDirectory}", logsDirectory);

            // Build the host
            _host = Host.CreateDefaultBuilder()
                .UseSerilog()
                .ConfigureServices((context, services) =>
                {
                    // Database - Use scoped to avoid threading issues
                    services.AddDbContext<EmailDbContext>(options =>
                        options.UseSqlite("Data Source=emailclient.db"), ServiceLifetime.Scoped);

                    // Services - Use scoped for services that use DbContext
                    services.AddSingleton<IImapService, ImapService>();
                    services.AddScoped<IEmailService, EmailService>();
                    services.AddScoped<IAccountService, AccountService>();
                    services.AddSingleton<ISettingsService, SettingsService>();
                    services.AddSingleton<ISyncService, SyncService>();
                    services.AddScoped<IMultiAccountService, MultiAccountService>();

                    // Google Services
                    services.AddScoped<IGoogleAuthService, GoogleAuthService>();
                    services.AddScoped<IGoogleContactsService, GoogleContactsService>();
                    services.AddScoped<IGoogleCalendarService, GoogleCalendarService>();
                    services.AddScoped<IGmailService, GmailService>();

                    // ViewModels - Use scoped to match service lifetimes
                    services.AddScoped<MainWindowViewModel>();
                    services.AddScoped<AccountSetupViewModel>();
                    services.AddScoped<AccountDashboardViewModel>();
                    services.AddScoped<SettingsViewModel>();
                    services.AddScoped<EmailListViewModel>();
                    services.AddScoped<EmailDetailViewModel>();
                    services.AddScoped<ContactsViewModel>();
                    services.AddScoped<CalendarViewModel>();

                    // Views
                    services.AddTransient<MainWindow>();
                })
                .Build();

            // Ensure database is created
            using (var scope = _host.Services.CreateScope())
            {
                var context = scope.ServiceProvider.GetRequiredService<EmailDbContext>();
                context.Database.EnsureCreated();
            }

            // Start the main window
            var mainWindow = _host.Services.GetRequiredService<MainWindow>();
            mainWindow.Show();

            // Start the sync service after the UI is loaded
            _ = Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(2000); // Wait 2 seconds for UI to fully load
                    using var scope = _host.Services.CreateScope();
                    var syncService = scope.ServiceProvider.GetRequiredService<ISyncService>();
                    await syncService.StartAsync();
                }
                catch (Exception ex)
                {
                    Log.Logger.Error(ex, "Failed to start sync service");
                }
            });

            base.OnStartup(e);
        }
        catch (Exception ex)
        {
            Log.Logger?.Error(ex, "Fatal error during application startup");
            MessageBox.Show($"Fatal error during startup: {ex.Message}", "Email Client Error", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown(1);
        }
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        // Stop the sync service
        if (_host != null)
        {
            try
            {
                using var scope = _host.Services.CreateScope();
                var syncService = scope.ServiceProvider.GetService<ISyncService>();
                if (syncService != null)
                {
                    await syncService.StopAsync();
                }
            }
            catch
            {
                // Ignore errors during shutdown
            }
        }

        _host?.Dispose();
        Log.CloseAndFlush();
        base.OnExit(e);
    }

    private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        var exception = e.ExceptionObject as Exception;
        Log.Logger?.Fatal(exception, "Unhandled exception occurred");

        if (e.IsTerminating)
        {
            MessageBox.Show($"A fatal error occurred: {exception?.Message}", "Email Client Fatal Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void OnDispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
    {
        Log.Logger?.Error(e.Exception, "Unhandled dispatcher exception occurred");

        MessageBox.Show($"An error occurred: {e.Exception.Message}", "Email Client Error", MessageBoxButton.OK, MessageBoxImage.Warning);

        // Mark as handled to prevent application crash
        e.Handled = true;
    }

    private void OnUnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
    {
        Log.Logger?.Error(e.Exception, "Unobserved task exception occurred");

        // Mark as observed to prevent application crash
        e.SetObserved();
    }
}
