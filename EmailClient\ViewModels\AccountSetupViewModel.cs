using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using EmailClient.Models;
using EmailClient.Services;
using Microsoft.Extensions.Logging;
using System.Windows;
using System.Windows.Media;
using System.Collections.ObjectModel;

namespace EmailClient.ViewModels;

public partial class AccountSetupViewModel : ViewModelBase
{
    private readonly IAccountService _accountService;
    private readonly IGoogleAuthService _googleAuthService;
    private readonly ILogger<AccountSetupViewModel> _logger;
    private Window? _window;

    [ObservableProperty]
    private string _accountName = string.Empty;

    [ObservableProperty]
    private string _emailAddress = string.Empty;

    [ObservableProperty]
    private AccountType _selectedAccountType = AccountType.IMAP;

    [ObservableProperty]
    private string _selectedOAuthProvider = "Google";

    [ObservableProperty]
    private string _username = string.Empty;

    [ObservableProperty]
    private string _password = string.Empty;

    [ObservableProperty]
    private string _imapServer = string.Empty;

    [ObservableProperty]
    private int _imapPort = 993;

    [ObservableProperty]
    private bool _useSsl = true;

    [ObservableProperty]
    private string _testResult = string.Empty;

    [ObservableProperty]
    private Brush _testResultColor = Brushes.Black;

    [ObservableProperty]
    private bool _isTesting = false;

    public bool DialogResult { get; private set; }

    public ObservableCollection<AccountType> AccountTypes { get; } = new()
    {
        AccountType.IMAP,
        AccountType.OAuth
    };

    public ObservableCollection<string> OAuthProviders { get; } = new()
    {
        "Google"
    };

    // Helper properties for UI visibility
    public bool IsImapAccount => SelectedAccountType == AccountType.IMAP;
    public bool IsOAuthAccount => SelectedAccountType == AccountType.OAuth;

    public AccountSetupViewModel(IAccountService accountService, IGoogleAuthService googleAuthService, ILogger<AccountSetupViewModel> logger)
    {
        _accountService = accountService;
        _googleAuthService = googleAuthService;
        _logger = logger;
    }

    public void SetWindow(Window window)
    {
        _window = window;
    }

    public bool CanTestConnection => !string.IsNullOrWhiteSpace(EmailAddress) &&
                                   !IsTesting &&
                                   (IsImapAccount ?
                                       (!string.IsNullOrWhiteSpace(Username) &&
                                        !string.IsNullOrWhiteSpace(Password) &&
                                        !string.IsNullOrWhiteSpace(ImapServer)) :
                                       true); // OAuth accounts don't need IMAP credentials

    public bool CanSave => !string.IsNullOrWhiteSpace(AccountName) &&
                          !string.IsNullOrWhiteSpace(EmailAddress) &&
                          !IsTesting &&
                          (IsImapAccount ?
                              (!string.IsNullOrWhiteSpace(Username) &&
                               !string.IsNullOrWhiteSpace(Password) &&
                               !string.IsNullOrWhiteSpace(ImapServer)) :
                              true); // OAuth accounts don't need IMAP credentials

    partial void OnEmailAddressChanged(string value)
    {
        if (string.IsNullOrWhiteSpace(Username))
        {
            Username = value;
        }
        if (string.IsNullOrWhiteSpace(AccountName) && !string.IsNullOrWhiteSpace(value))
        {
            AccountName = value;
        }
        OnPropertyChanged(nameof(CanTestConnection));
        OnPropertyChanged(nameof(CanSave));
    }

    partial void OnUsernameChanged(string value)
    {
        OnPropertyChanged(nameof(CanTestConnection));
        OnPropertyChanged(nameof(CanSave));
    }

    partial void OnPasswordChanged(string value)
    {
        OnPropertyChanged(nameof(CanTestConnection));
        OnPropertyChanged(nameof(CanSave));
    }

    partial void OnAccountNameChanged(string value)
    {
        OnPropertyChanged(nameof(CanSave));
    }

    partial void OnImapServerChanged(string value)
    {
        OnPropertyChanged(nameof(CanTestConnection));
        OnPropertyChanged(nameof(CanSave));
    }

    partial void OnIsTestingChanged(bool value)
    {
        OnPropertyChanged(nameof(CanTestConnection));
        OnPropertyChanged(nameof(CanSave));
    }

    partial void OnSelectedAccountTypeChanged(AccountType value)
    {
        OnPropertyChanged(nameof(IsImapAccount));
        OnPropertyChanged(nameof(IsOAuthAccount));
        OnPropertyChanged(nameof(CanTestConnection));
        OnPropertyChanged(nameof(CanSave));

        // Clear test result when switching account types
        TestResult = string.Empty;
        TestResultColor = Brushes.Black;
    }

    [RelayCommand]
    private void SetupGmail()
    {
        ImapServer = "imap.gmail.com";
        ImapPort = 993;
        UseSsl = true;
        TestResult = "Gmail settings applied. Use an app-specific password.";
        TestResultColor = Brushes.Blue;
    }

    [RelayCommand]
    private void SetupOutlook()
    {
        ImapServer = "outlook.office365.com";
        ImapPort = 993;
        UseSsl = true;
        TestResult = "Outlook settings applied.";
        TestResultColor = Brushes.Blue;
    }

    [RelayCommand]
    private void SetupYahoo()
    {
        ImapServer = "imap.mail.yahoo.com";
        ImapPort = 993;
        UseSsl = true;
        TestResult = "Yahoo settings applied.";
        TestResultColor = Brushes.Blue;
    }

    [RelayCommand]
    private async Task TestConnection()
    {
        IsTesting = true;
        TestResult = "Testing connection...";
        TestResultColor = Brushes.Blue;

        try
        {
            if (IsOAuthAccount)
            {
                // For OAuth accounts, we need to check if Google authentication is available
                if (SelectedOAuthProvider.Equals("Google", StringComparison.OrdinalIgnoreCase))
                {
                    var isAuthenticated = await _googleAuthService.IsAuthenticatedAsync();
                    if (isAuthenticated)
                    {
                        TestResult = "✓ Google OAuth authentication is active!";
                        TestResultColor = Brushes.Green;
                    }
                    else
                    {
                        TestResult = "⚠ Google OAuth not authenticated. Please authenticate in Settings first.";
                        TestResultColor = Brushes.Orange;
                    }
                }
                else
                {
                    TestResult = $"✗ Unsupported OAuth provider: {SelectedOAuthProvider}";
                    TestResultColor = Brushes.Red;
                }
            }
            else
            {
                // For IMAP accounts, test the connection normally
                var testAccount = CreateEmailAccount();
                var success = await _accountService.TestAccountConnectionAsync(testAccount);

                if (success)
                {
                    TestResult = "✓ IMAP connection successful!";
                    TestResultColor = Brushes.Green;
                }
                else
                {
                    TestResult = "✗ IMAP connection failed. Check your settings.";
                    TestResultColor = Brushes.Red;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Connection test failed");
            TestResult = $"✗ Error: {ex.Message}";
            TestResultColor = Brushes.Red;
        }
        finally
        {
            IsTesting = false;
        }
    }

    [RelayCommand]
    private async Task Save()
    {
        try
        {
            var account = CreateEmailAccount();

            // For OAuth accounts, get the email address from the provider if not set
            if (IsOAuthAccount && string.IsNullOrWhiteSpace(EmailAddress))
            {
                if (SelectedOAuthProvider.Equals("Google", StringComparison.OrdinalIgnoreCase))
                {
                    var userInfo = await _googleAuthService.GetUserInfoAsync();
                    if (userInfo != null && !string.IsNullOrWhiteSpace(userInfo.Email))
                    {
                        account.EmailAddress = userInfo.Email;
                        EmailAddress = userInfo.Email; // Update the UI
                    }
                }
            }

            await _accountService.CreateAccountAsync(account);

            DialogResult = true;
            _window?.Close();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save account");
            MessageBox.Show($"Failed to save account: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    [RelayCommand]
    private void Cancel()
    {
        DialogResult = false;
        _window?.Close();
    }

    private EmailAccount CreateEmailAccount()
    {
        var account = new EmailAccount
        {
            Name = AccountName.Trim(),
            EmailAddress = EmailAddress.Trim(),
            AccountType = SelectedAccountType,
            IsEnabled = true
        };

        if (IsImapAccount)
        {
            account.Username = Username.Trim();
            account.Password = Password; // In production, this should be encrypted
            account.ImapServer = ImapServer.Trim();
            account.ImapPort = ImapPort;
            account.UseSsl = UseSsl;
        }
        else if (IsOAuthAccount)
        {
            account.OAuthProvider = SelectedOAuthProvider;
        }

        return account;
    }
}
