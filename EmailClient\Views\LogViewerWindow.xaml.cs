using System;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Threading;

namespace EmailClient.Views
{
    public partial class LogViewerWindow : Window
    {
        private readonly DispatcherTimer _refreshTimer;
        private string _logFilePath;
        private long _lastFileSize = 0;

        public LogViewerWindow()
        {
            InitializeComponent();
            
            // Find the most recent log file
            FindLatestLogFile();
            
            // Set up auto-refresh timer
            _refreshTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _refreshTimer.Tick += RefreshTimer_Tick;
            _refreshTimer.Start();
            
            // Load initial content
            LoadLogContent();
        }

        private void FindLatestLogFile()
        {
            try
            {
                // Try multiple possible locations for log files
                var possibleLogDirectories = new[]
                {
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs"),
                    Path.Combine(Directory.GetCurrentDirectory(), "logs"),
                    Path.Combine(Environment.CurrentDirectory, "logs"),
                    // Go up from bin/Debug/net8.0-windows to project root
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "logs"),
                    // Direct path to project logs
                    @"c:\Users\<USER>\dev\windows app\EmailClient\logs"
                };

                foreach (var logsDirectory in possibleLogDirectories)
                {
                    var normalizedPath = Path.GetFullPath(logsDirectory);
                    if (Directory.Exists(normalizedPath))
                    {
                        var logFiles = Directory.GetFiles(normalizedPath, "emailclient-*.txt")
                                              .OrderByDescending(f => File.GetLastWriteTime(f))
                                              .ToArray();

                        if (logFiles.Length > 0)
                        {
                            _logFilePath = logFiles[0];
                            StatusTextBlock.Text = $"Monitoring: {Path.GetFileName(_logFilePath)} in {normalizedPath}";
                            return;
                        }
                    }
                }

                // If no logs found, show debug info
                StatusTextBlock.Text = $"No log files found. Checked: AppDomain.BaseDirectory={AppDomain.CurrentDomain.BaseDirectory}, CurrentDirectory={Directory.GetCurrentDirectory()}";
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = $"Error finding log files: {ex.Message}";
            }
        }

        private void LoadLogContent()
        {
            if (string.IsNullOrEmpty(_logFilePath) || !File.Exists(_logFilePath))
                return;

            // Retry mechanism for file access issues
            for (int attempt = 0; attempt < 3; attempt++)
            {
                try
                {
                    string content;
                    // Use FileShare.ReadWrite to allow reading while Serilog is writing
                    using (var fileStream = new FileStream(_logFilePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                    using (var reader = new StreamReader(fileStream))
                    {
                        content = reader.ReadToEnd();
                    }

                    var filteredContent = ApplyFilter(content);

                    LogTextBox.Text = filteredContent;
                    _lastFileSize = new FileInfo(_logFilePath).Length;

                    if (AutoScrollCheckBox.IsChecked == true)
                    {
                        LogScrollViewer.ScrollToEnd();
                    }

                    // Clear any previous error message
                    if (StatusTextBlock.Text.StartsWith("Error reading"))
                    {
                        StatusTextBlock.Text = $"Monitoring: {Path.GetFileName(_logFilePath)}";
                    }

                    return; // Success, exit retry loop
                }
                catch (Exception ex)
                {
                    if (attempt == 2) // Last attempt
                    {
                        StatusTextBlock.Text = $"Error reading log file (after 3 attempts): {ex.Message}";
                    }
                    else
                    {
                        // Wait a bit before retrying
                        System.Threading.Thread.Sleep(100);
                    }
                }
            }
        }

        private string ApplyFilter(string content)
        {
            var filter = FilterTextBox.Text?.Trim();
            if (string.IsNullOrEmpty(filter))
                return content;

            var lines = content.Split('\n');
            var filteredLines = lines.Where(line => line.Contains(filter, StringComparison.OrdinalIgnoreCase));
            return string.Join('\n', filteredLines);
        }

        private void RefreshTimer_Tick(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(_logFilePath) || !File.Exists(_logFilePath))
                return;

            try
            {
                var currentFileSize = new FileInfo(_logFilePath).Length;
                if (currentFileSize != _lastFileSize)
                {
                    LoadLogContent();
                }
            }
            catch
            {
                // Ignore errors during auto-refresh
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadLogContent();
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            LogTextBox.Clear();
        }

        private void CopyButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(LogTextBox.Text))
                {
                    Clipboard.SetText(LogTextBox.Text);
                    StatusTextBlock.Text = "Log content copied to clipboard";
                }
                else
                {
                    StatusTextBlock.Text = "No log content to copy";
                }
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = $"Failed to copy to clipboard: {ex.Message}";
            }
        }

        private void FilterTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            LoadLogContent();
        }

        protected override void OnClosed(EventArgs e)
        {
            _refreshTimer?.Stop();
            base.OnClosed(e);
        }
    }
}
