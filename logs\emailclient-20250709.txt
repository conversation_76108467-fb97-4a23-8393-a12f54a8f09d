2025-07-09 18:05:31.043 -07:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-07-09 18:05:31.168 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Accounts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Accounts" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "EmailAddress" TEXT NOT NULL,
    "ImapServer" TEXT NOT NULL,
    "ImapPort" INTEGER NOT NULL,
    "UseSsl" INTEGER NOT NULL,
    "Username" TEXT NOT NULL,
    "Password" TEXT NOT NULL,
    "IsEnabled" INTEGER NOT NULL,
    "CreatedAt" TEXT NOT NULL,
    "LastSyncAt" TEXT NOT NULL
);
2025-07-09 18:05:31.170 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "CalendarEvents" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_CalendarEvents" PRIMARY KEY AUTOINCREMENT,
    "GoogleId" TEXT NOT NULL,
    "CalendarId" TEXT NOT NULL,
    "Summary" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Location" TEXT NOT NULL,
    "StartDateTime" TEXT NULL,
    "EndDateTime" TEXT NULL,
    "IsAllDay" INTEGER NOT NULL,
    "TimeZone" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "Visibility" TEXT NOT NULL,
    "Attendees" TEXT NOT NULL,
    "Organizer" TEXT NOT NULL,
    "Creator" TEXT NOT NULL,
    "RecurrenceRule" TEXT NOT NULL,
    "RecurringEventId" TEXT NOT NULL,
    "HtmlLink" TEXT NOT NULL,
    "HangoutLink" TEXT NOT NULL,
    "ConferenceData" TEXT NOT NULL,
    "Reminders" TEXT NOT NULL,
    "CreatedAt" TEXT NOT NULL,
    "UpdatedAt" TEXT NOT NULL,
    "LastSyncAt" TEXT NOT NULL,
    "ETag" TEXT NOT NULL,
    "IsDeleted" INTEGER NOT NULL
);
2025-07-09 18:05:31.170 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Contacts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Contacts" PRIMARY KEY AUTOINCREMENT,
    "GoogleId" TEXT NOT NULL,
    "DisplayName" TEXT NOT NULL,
    "GivenName" TEXT NOT NULL,
    "FamilyName" TEXT NOT NULL,
    "MiddleName" TEXT NOT NULL,
    "EmailAddresses" TEXT NOT NULL,
    "PhoneNumbers" TEXT NOT NULL,
    "Addresses" TEXT NOT NULL,
    "Organizations" TEXT NOT NULL,
    "PhotoUrl" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "Birthday" TEXT NULL,
    "CreatedAt" TEXT NOT NULL,
    "UpdatedAt" TEXT NOT NULL,
    "LastSyncAt" TEXT NOT NULL,
    "ETag" TEXT NOT NULL,
    "IsDeleted" INTEGER NOT NULL
);
2025-07-09 18:05:31.170 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "GoogleApiSettings" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_GoogleApiSettings" PRIMARY KEY AUTOINCREMENT,
    "ClientId" TEXT NOT NULL,
    "ClientSecret" TEXT NOT NULL,
    "AccessToken" TEXT NOT NULL,
    "RefreshToken" TEXT NOT NULL,
    "TokenExpiresAt" TEXT NULL,
    "Scopes" TEXT NOT NULL,
    "ContactsSyncEnabled" INTEGER NOT NULL,
    "CalendarSyncEnabled" INTEGER NOT NULL,
    "ContactsSyncIntervalMinutes" INTEGER NOT NULL,
    "CalendarSyncIntervalMinutes" INTEGER NOT NULL,
    "LastContactsSync" TEXT NULL,
    "LastCalendarSync" TEXT NULL,
    "ContactsSyncToken" TEXT NOT NULL,
    "CalendarSyncToken" TEXT NOT NULL,
    "IsAuthenticated" INTEGER NOT NULL,
    "CreatedAt" TEXT NOT NULL,
    "UpdatedAt" TEXT NOT NULL
);
2025-07-09 18:05:31.170 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Folders" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Folders" PRIMARY KEY AUTOINCREMENT,
    "AccountId" INTEGER NOT NULL,
    "Name" TEXT NOT NULL,
    "FullName" TEXT NOT NULL,
    "ParentFolderId" INTEGER NULL,
    "Type" INTEGER NOT NULL,
    "UnreadCount" INTEGER NOT NULL,
    "TotalCount" INTEGER NOT NULL,
    "LastSyncAt" TEXT NOT NULL,
    CONSTRAINT "FK_Folders_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_Folders_Folders_ParentFolderId" FOREIGN KEY ("ParentFolderId") REFERENCES "Folders" ("Id") ON DELETE RESTRICT
);
2025-07-09 18:05:31.171 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Messages" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Messages" PRIMARY KEY AUTOINCREMENT,
    "MessageId" TEXT NOT NULL,
    "AccountId" INTEGER NOT NULL,
    "FolderId" INTEGER NULL,
    "Subject" TEXT NOT NULL,
    "FromAddress" TEXT NOT NULL,
    "FromName" TEXT NOT NULL,
    "ToAddresses" TEXT NOT NULL,
    "CcAddresses" TEXT NOT NULL,
    "BccAddresses" TEXT NOT NULL,
    "DateSent" TEXT NOT NULL,
    "DateReceived" TEXT NOT NULL,
    "TextBody" TEXT NOT NULL,
    "HtmlBody" TEXT NOT NULL,
    "IsRead" INTEGER NOT NULL,
    "IsFlagged" INTEGER NOT NULL,
    "IsDeleted" INTEGER NOT NULL,
    "Size" INTEGER NOT NULL,
    "HasAttachments" INTEGER NOT NULL,
    CONSTRAINT "FK_Messages_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_Messages_Folders_FolderId" FOREIGN KEY ("FolderId") REFERENCES "Folders" ("Id") ON DELETE SET NULL
);
2025-07-09 18:05:31.171 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Attachments" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Attachments" PRIMARY KEY AUTOINCREMENT,
    "MessageId" INTEGER NOT NULL,
    "FileName" TEXT NOT NULL,
    "ContentType" TEXT NOT NULL,
    "Size" INTEGER NOT NULL,
    "ContentId" TEXT NOT NULL,
    "IsInline" INTEGER NOT NULL,
    "Data" BLOB NULL,
    "FilePath" TEXT NULL,
    "CreatedAt" TEXT NOT NULL,
    CONSTRAINT "FK_Attachments_Messages_MessageId" FOREIGN KEY ("MessageId") REFERENCES "Messages" ("Id") ON DELETE CASCADE
);
2025-07-09 18:05:31.171 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Accounts_EmailAddress" ON "Accounts" ("EmailAddress");
2025-07-09 18:05:31.171 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Attachments_MessageId" ON "Attachments" ("MessageId");
2025-07-09 18:05:31.171 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_CalendarEvents_GoogleId_CalendarId" ON "CalendarEvents" ("GoogleId", "CalendarId");
2025-07-09 18:05:31.171 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Contacts_GoogleId" ON "Contacts" ("GoogleId");
2025-07-09 18:05:31.171 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Folders_AccountId_FullName" ON "Folders" ("AccountId", "FullName");
2025-07-09 18:05:31.171 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Folders_ParentFolderId" ON "Folders" ("ParentFolderId");
2025-07-09 18:05:31.171 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Messages_AccountId_MessageId" ON "Messages" ("AccountId", "MessageId");
2025-07-09 18:05:31.172 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Messages_FolderId" ON "Messages" ("FolderId");
2025-07-09 18:05:32.350 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
ORDER BY "a"."Name"
2025-07-09 18:05:32.423 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
2025-07-09 18:05:32.427 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0 AND NOT ("m"."IsRead")
2025-07-09 18:05:32.451 -07:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."AccountId", "t"."BccAddresses", "t"."CcAddresses", "t"."DateReceived", "t"."DateSent", "t"."FolderId", "t"."FromAddress", "t"."FromName", "t"."HasAttachments", "t"."HtmlBody", "t"."IsDeleted", "t"."IsFlagged", "t"."IsRead", "t"."MessageId", "t"."Size", "t"."Subject", "t"."TextBody", "t"."ToAddresses", "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username", "t"."Id0", "t"."AccountId0", "t"."FullName", "t"."LastSyncAt", "t"."Name", "t"."ParentFolderId", "t"."TotalCount", "t"."Type", "t"."UnreadCount"
FROM (
    SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id" AS "Id0", "f"."AccountId" AS "AccountId0", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
    FROM "Messages" AS "m"
    LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
    WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
    ORDER BY "m"."DateReceived" DESC
    LIMIT @__p_1 OFFSET @__p_0
) AS "t"
INNER JOIN "Accounts" AS "a" ON "t"."AccountId" = "a"."Id"
ORDER BY "t"."DateReceived" DESC
2025-07-09 18:05:32.482 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."AccountId", "m"."Id", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id", "f"."AccountId", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
ORDER BY "m"."AccountId"
2025-07-09 18:05:33.596 -07:00 [INF] Starting sync service
2025-07-09 18:05:33.598 -07:00 [INF] Starting background sync
2025-07-09 18:05:33.636 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
WHERE "a"."IsEnabled"
2025-07-09 18:05:33.646 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "GoogleApiSettings" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_GoogleApiSettings" PRIMARY KEY AUTOINCREMENT,
                    "ClientId" TEXT NOT NULL DEFAULT '',
                    "ClientSecret" TEXT NOT NULL DEFAULT '',
                    "AccessToken" TEXT NOT NULL DEFAULT '',
                    "RefreshToken" TEXT NOT NULL DEFAULT '',
                    "TokenExpiresAt" TEXT,
                    "Scopes" TEXT NOT NULL DEFAULT '',
                    "ContactsSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "CalendarSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "ContactsSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "CalendarSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "LastContactsSync" TEXT,
                    "LastCalendarSync" TEXT,
                    "ContactsSyncToken" TEXT NOT NULL DEFAULT '',
                    "CalendarSyncToken" TEXT NOT NULL DEFAULT '',
                    "IsAuthenticated" INTEGER NOT NULL DEFAULT 0,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now'))
                );
2025-07-09 18:05:33.647 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "CalendarEvents" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_CalendarEvents" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "CalendarId" TEXT NOT NULL DEFAULT '',
                    "Summary" TEXT NOT NULL DEFAULT '',
                    "Description" TEXT NOT NULL DEFAULT '',
                    "Location" TEXT NOT NULL DEFAULT '',
                    "StartDateTime" TEXT,
                    "EndDateTime" TEXT,
                    "IsAllDay" INTEGER NOT NULL DEFAULT 0,
                    "TimeZone" TEXT NOT NULL DEFAULT '',
                    "Status" TEXT NOT NULL DEFAULT '',
                    "Visibility" TEXT NOT NULL DEFAULT '',
                    "Attendees" TEXT NOT NULL DEFAULT '',
                    "Organizer" TEXT NOT NULL DEFAULT '',
                    "Creator" TEXT NOT NULL DEFAULT '',
                    "RecurrenceRule" TEXT NOT NULL DEFAULT '',
                    "RecurringEventId" TEXT NOT NULL DEFAULT '',
                    "HtmlLink" TEXT NOT NULL DEFAULT '',
                    "HangoutLink" TEXT NOT NULL DEFAULT '',
                    "ConferenceData" TEXT NOT NULL DEFAULT '',
                    "Reminders" TEXT NOT NULL DEFAULT '',
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:05:33.647 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "Contacts" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_Contacts" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "DisplayName" TEXT NOT NULL DEFAULT '',
                    "GivenName" TEXT NOT NULL DEFAULT '',
                    "FamilyName" TEXT NOT NULL DEFAULT '',
                    "MiddleName" TEXT NOT NULL DEFAULT '',
                    "EmailAddresses" TEXT NOT NULL DEFAULT '',
                    "PhoneNumbers" TEXT NOT NULL DEFAULT '',
                    "Addresses" TEXT NOT NULL DEFAULT '',
                    "Organizations" TEXT NOT NULL DEFAULT '',
                    "PhotoUrl" TEXT NOT NULL DEFAULT '',
                    "Notes" TEXT NOT NULL DEFAULT '',
                    "Birthday" TEXT,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:05:33.648 -07:00 [INF] Google API tables ensured to exist
2025-07-09 18:05:33.650 -07:00 [WRN] The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-07-09 18:05:33.654 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "g"."Id", "g"."AccessToken", "g"."CalendarSyncEnabled", "g"."CalendarSyncIntervalMinutes", "g"."CalendarSyncToken", "g"."ClientId", "g"."ClientSecret", "g"."ContactsSyncEnabled", "g"."ContactsSyncIntervalMinutes", "g"."ContactsSyncToken", "g"."CreatedAt", "g"."IsAuthenticated", "g"."LastCalendarSync", "g"."LastContactsSync", "g"."RefreshToken", "g"."Scopes", "g"."TokenExpiresAt", "g"."UpdatedAt"
FROM "GoogleApiSettings" AS "g"
LIMIT 1
2025-07-09 18:05:33.655 -07:00 [WRN] No Google API settings found
2025-07-09 18:05:33.655 -07:00 [INF] Background sync completed successfully
2025-07-09 18:18:37.878 -07:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-09 18:18:39.056 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
ORDER BY "a"."Name"
2025-07-09 18:18:39.127 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
2025-07-09 18:18:39.131 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0 AND NOT ("m"."IsRead")
2025-07-09 18:18:39.154 -07:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."AccountId", "t"."BccAddresses", "t"."CcAddresses", "t"."DateReceived", "t"."DateSent", "t"."FolderId", "t"."FromAddress", "t"."FromName", "t"."HasAttachments", "t"."HtmlBody", "t"."IsDeleted", "t"."IsFlagged", "t"."IsRead", "t"."MessageId", "t"."Size", "t"."Subject", "t"."TextBody", "t"."ToAddresses", "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username", "t"."Id0", "t"."AccountId0", "t"."FullName", "t"."LastSyncAt", "t"."Name", "t"."ParentFolderId", "t"."TotalCount", "t"."Type", "t"."UnreadCount"
FROM (
    SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id" AS "Id0", "f"."AccountId" AS "AccountId0", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
    FROM "Messages" AS "m"
    LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
    WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
    ORDER BY "m"."DateReceived" DESC
    LIMIT @__p_1 OFFSET @__p_0
) AS "t"
INNER JOIN "Accounts" AS "a" ON "t"."AccountId" = "a"."Id"
ORDER BY "t"."DateReceived" DESC
2025-07-09 18:18:39.175 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."AccountId", "m"."Id", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id", "f"."AccountId", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
ORDER BY "m"."AccountId"
2025-07-09 18:18:40.326 -07:00 [INF] Starting sync service
2025-07-09 18:18:40.329 -07:00 [INF] Starting background sync
2025-07-09 18:18:40.372 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
WHERE "a"."IsEnabled"
2025-07-09 18:18:40.383 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "GoogleApiSettings" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_GoogleApiSettings" PRIMARY KEY AUTOINCREMENT,
                    "ClientId" TEXT NOT NULL DEFAULT '',
                    "ClientSecret" TEXT NOT NULL DEFAULT '',
                    "AccessToken" TEXT NOT NULL DEFAULT '',
                    "RefreshToken" TEXT NOT NULL DEFAULT '',
                    "TokenExpiresAt" TEXT,
                    "Scopes" TEXT NOT NULL DEFAULT '',
                    "ContactsSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "CalendarSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "ContactsSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "CalendarSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "LastContactsSync" TEXT,
                    "LastCalendarSync" TEXT,
                    "ContactsSyncToken" TEXT NOT NULL DEFAULT '',
                    "CalendarSyncToken" TEXT NOT NULL DEFAULT '',
                    "IsAuthenticated" INTEGER NOT NULL DEFAULT 0,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now'))
                );
2025-07-09 18:18:40.384 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "CalendarEvents" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_CalendarEvents" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "CalendarId" TEXT NOT NULL DEFAULT '',
                    "Summary" TEXT NOT NULL DEFAULT '',
                    "Description" TEXT NOT NULL DEFAULT '',
                    "Location" TEXT NOT NULL DEFAULT '',
                    "StartDateTime" TEXT,
                    "EndDateTime" TEXT,
                    "IsAllDay" INTEGER NOT NULL DEFAULT 0,
                    "TimeZone" TEXT NOT NULL DEFAULT '',
                    "Status" TEXT NOT NULL DEFAULT '',
                    "Visibility" TEXT NOT NULL DEFAULT '',
                    "Attendees" TEXT NOT NULL DEFAULT '',
                    "Organizer" TEXT NOT NULL DEFAULT '',
                    "Creator" TEXT NOT NULL DEFAULT '',
                    "RecurrenceRule" TEXT NOT NULL DEFAULT '',
                    "RecurringEventId" TEXT NOT NULL DEFAULT '',
                    "HtmlLink" TEXT NOT NULL DEFAULT '',
                    "HangoutLink" TEXT NOT NULL DEFAULT '',
                    "ConferenceData" TEXT NOT NULL DEFAULT '',
                    "Reminders" TEXT NOT NULL DEFAULT '',
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:18:40.384 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "Contacts" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_Contacts" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "DisplayName" TEXT NOT NULL DEFAULT '',
                    "GivenName" TEXT NOT NULL DEFAULT '',
                    "FamilyName" TEXT NOT NULL DEFAULT '',
                    "MiddleName" TEXT NOT NULL DEFAULT '',
                    "EmailAddresses" TEXT NOT NULL DEFAULT '',
                    "PhoneNumbers" TEXT NOT NULL DEFAULT '',
                    "Addresses" TEXT NOT NULL DEFAULT '',
                    "Organizations" TEXT NOT NULL DEFAULT '',
                    "PhotoUrl" TEXT NOT NULL DEFAULT '',
                    "Notes" TEXT NOT NULL DEFAULT '',
                    "Birthday" TEXT,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:18:40.384 -07:00 [INF] Google API tables ensured to exist
2025-07-09 18:18:40.387 -07:00 [WRN] The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-07-09 18:18:40.391 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "g"."Id", "g"."AccessToken", "g"."CalendarSyncEnabled", "g"."CalendarSyncIntervalMinutes", "g"."CalendarSyncToken", "g"."ClientId", "g"."ClientSecret", "g"."ContactsSyncEnabled", "g"."ContactsSyncIntervalMinutes", "g"."ContactsSyncToken", "g"."CreatedAt", "g"."IsAuthenticated", "g"."LastCalendarSync", "g"."LastContactsSync", "g"."RefreshToken", "g"."Scopes", "g"."TokenExpiresAt", "g"."UpdatedAt"
FROM "GoogleApiSettings" AS "g"
LIMIT 1
2025-07-09 18:18:40.391 -07:00 [WRN] No Google API settings found
2025-07-09 18:18:40.391 -07:00 [INF] Background sync completed successfully
2025-07-09 18:30:25.351 -07:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-09 18:30:26.530 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
ORDER BY "a"."Name"
2025-07-09 18:30:26.597 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
2025-07-09 18:30:26.600 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0 AND NOT ("m"."IsRead")
2025-07-09 18:30:26.623 -07:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."AccountId", "t"."BccAddresses", "t"."CcAddresses", "t"."DateReceived", "t"."DateSent", "t"."FolderId", "t"."FromAddress", "t"."FromName", "t"."HasAttachments", "t"."HtmlBody", "t"."IsDeleted", "t"."IsFlagged", "t"."IsRead", "t"."MessageId", "t"."Size", "t"."Subject", "t"."TextBody", "t"."ToAddresses", "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username", "t"."Id0", "t"."AccountId0", "t"."FullName", "t"."LastSyncAt", "t"."Name", "t"."ParentFolderId", "t"."TotalCount", "t"."Type", "t"."UnreadCount"
FROM (
    SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id" AS "Id0", "f"."AccountId" AS "AccountId0", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
    FROM "Messages" AS "m"
    LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
    WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
    ORDER BY "m"."DateReceived" DESC
    LIMIT @__p_1 OFFSET @__p_0
) AS "t"
INNER JOIN "Accounts" AS "a" ON "t"."AccountId" = "a"."Id"
ORDER BY "t"."DateReceived" DESC
2025-07-09 18:30:26.644 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."AccountId", "m"."Id", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id", "f"."AccountId", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
ORDER BY "m"."AccountId"
2025-07-09 18:30:27.776 -07:00 [INF] Starting sync service
2025-07-09 18:30:27.778 -07:00 [INF] Starting background sync
2025-07-09 18:30:27.823 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
WHERE "a"."IsEnabled"
2025-07-09 18:30:27.832 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "GoogleApiSettings" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_GoogleApiSettings" PRIMARY KEY AUTOINCREMENT,
                    "ClientId" TEXT NOT NULL DEFAULT '',
                    "ClientSecret" TEXT NOT NULL DEFAULT '',
                    "AccessToken" TEXT NOT NULL DEFAULT '',
                    "RefreshToken" TEXT NOT NULL DEFAULT '',
                    "TokenExpiresAt" TEXT,
                    "Scopes" TEXT NOT NULL DEFAULT '',
                    "ContactsSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "CalendarSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "ContactsSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "CalendarSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "LastContactsSync" TEXT,
                    "LastCalendarSync" TEXT,
                    "ContactsSyncToken" TEXT NOT NULL DEFAULT '',
                    "CalendarSyncToken" TEXT NOT NULL DEFAULT '',
                    "IsAuthenticated" INTEGER NOT NULL DEFAULT 0,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now'))
                );
2025-07-09 18:30:27.834 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "CalendarEvents" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_CalendarEvents" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "CalendarId" TEXT NOT NULL DEFAULT '',
                    "Summary" TEXT NOT NULL DEFAULT '',
                    "Description" TEXT NOT NULL DEFAULT '',
                    "Location" TEXT NOT NULL DEFAULT '',
                    "StartDateTime" TEXT,
                    "EndDateTime" TEXT,
                    "IsAllDay" INTEGER NOT NULL DEFAULT 0,
                    "TimeZone" TEXT NOT NULL DEFAULT '',
                    "Status" TEXT NOT NULL DEFAULT '',
                    "Visibility" TEXT NOT NULL DEFAULT '',
                    "Attendees" TEXT NOT NULL DEFAULT '',
                    "Organizer" TEXT NOT NULL DEFAULT '',
                    "Creator" TEXT NOT NULL DEFAULT '',
                    "RecurrenceRule" TEXT NOT NULL DEFAULT '',
                    "RecurringEventId" TEXT NOT NULL DEFAULT '',
                    "HtmlLink" TEXT NOT NULL DEFAULT '',
                    "HangoutLink" TEXT NOT NULL DEFAULT '',
                    "ConferenceData" TEXT NOT NULL DEFAULT '',
                    "Reminders" TEXT NOT NULL DEFAULT '',
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:30:27.834 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "Contacts" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_Contacts" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "DisplayName" TEXT NOT NULL DEFAULT '',
                    "GivenName" TEXT NOT NULL DEFAULT '',
                    "FamilyName" TEXT NOT NULL DEFAULT '',
                    "MiddleName" TEXT NOT NULL DEFAULT '',
                    "EmailAddresses" TEXT NOT NULL DEFAULT '',
                    "PhoneNumbers" TEXT NOT NULL DEFAULT '',
                    "Addresses" TEXT NOT NULL DEFAULT '',
                    "Organizations" TEXT NOT NULL DEFAULT '',
                    "PhotoUrl" TEXT NOT NULL DEFAULT '',
                    "Notes" TEXT NOT NULL DEFAULT '',
                    "Birthday" TEXT,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:30:27.834 -07:00 [INF] Google API tables ensured to exist
2025-07-09 18:30:27.837 -07:00 [WRN] The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-07-09 18:30:27.841 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "g"."Id", "g"."AccessToken", "g"."CalendarSyncEnabled", "g"."CalendarSyncIntervalMinutes", "g"."CalendarSyncToken", "g"."ClientId", "g"."ClientSecret", "g"."ContactsSyncEnabled", "g"."ContactsSyncIntervalMinutes", "g"."ContactsSyncToken", "g"."CreatedAt", "g"."IsAuthenticated", "g"."LastCalendarSync", "g"."LastContactsSync", "g"."RefreshToken", "g"."Scopes", "g"."TokenExpiresAt", "g"."UpdatedAt"
FROM "GoogleApiSettings" AS "g"
LIMIT 1
2025-07-09 18:30:27.841 -07:00 [WRN] No Google API settings found
2025-07-09 18:30:27.841 -07:00 [INF] Background sync completed successfully
2025-07-09 18:31:15.713 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
ORDER BY "a"."Name"
2025-07-09 18:31:15.719 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "GoogleApiSettings" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_GoogleApiSettings" PRIMARY KEY AUTOINCREMENT,
                    "ClientId" TEXT NOT NULL DEFAULT '',
                    "ClientSecret" TEXT NOT NULL DEFAULT '',
                    "AccessToken" TEXT NOT NULL DEFAULT '',
                    "RefreshToken" TEXT NOT NULL DEFAULT '',
                    "TokenExpiresAt" TEXT,
                    "Scopes" TEXT NOT NULL DEFAULT '',
                    "ContactsSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "CalendarSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "ContactsSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "CalendarSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "LastContactsSync" TEXT,
                    "LastCalendarSync" TEXT,
                    "ContactsSyncToken" TEXT NOT NULL DEFAULT '',
                    "CalendarSyncToken" TEXT NOT NULL DEFAULT '',
                    "IsAuthenticated" INTEGER NOT NULL DEFAULT 0,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now'))
                );
2025-07-09 18:31:15.719 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "CalendarEvents" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_CalendarEvents" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "CalendarId" TEXT NOT NULL DEFAULT '',
                    "Summary" TEXT NOT NULL DEFAULT '',
                    "Description" TEXT NOT NULL DEFAULT '',
                    "Location" TEXT NOT NULL DEFAULT '',
                    "StartDateTime" TEXT,
                    "EndDateTime" TEXT,
                    "IsAllDay" INTEGER NOT NULL DEFAULT 0,
                    "TimeZone" TEXT NOT NULL DEFAULT '',
                    "Status" TEXT NOT NULL DEFAULT '',
                    "Visibility" TEXT NOT NULL DEFAULT '',
                    "Attendees" TEXT NOT NULL DEFAULT '',
                    "Organizer" TEXT NOT NULL DEFAULT '',
                    "Creator" TEXT NOT NULL DEFAULT '',
                    "RecurrenceRule" TEXT NOT NULL DEFAULT '',
                    "RecurringEventId" TEXT NOT NULL DEFAULT '',
                    "HtmlLink" TEXT NOT NULL DEFAULT '',
                    "HangoutLink" TEXT NOT NULL DEFAULT '',
                    "ConferenceData" TEXT NOT NULL DEFAULT '',
                    "Reminders" TEXT NOT NULL DEFAULT '',
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:31:15.719 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "Contacts" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_Contacts" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "DisplayName" TEXT NOT NULL DEFAULT '',
                    "GivenName" TEXT NOT NULL DEFAULT '',
                    "FamilyName" TEXT NOT NULL DEFAULT '',
                    "MiddleName" TEXT NOT NULL DEFAULT '',
                    "EmailAddresses" TEXT NOT NULL DEFAULT '',
                    "PhoneNumbers" TEXT NOT NULL DEFAULT '',
                    "Addresses" TEXT NOT NULL DEFAULT '',
                    "Organizations" TEXT NOT NULL DEFAULT '',
                    "PhotoUrl" TEXT NOT NULL DEFAULT '',
                    "Notes" TEXT NOT NULL DEFAULT '',
                    "Birthday" TEXT,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:31:15.719 -07:00 [INF] Google API tables ensured to exist
2025-07-09 18:31:15.719 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "g"."Id", "g"."AccessToken", "g"."CalendarSyncEnabled", "g"."CalendarSyncIntervalMinutes", "g"."CalendarSyncToken", "g"."ClientId", "g"."ClientSecret", "g"."ContactsSyncEnabled", "g"."ContactsSyncIntervalMinutes", "g"."ContactsSyncToken", "g"."CreatedAt", "g"."IsAuthenticated", "g"."LastCalendarSync", "g"."LastContactsSync", "g"."RefreshToken", "g"."Scopes", "g"."TokenExpiresAt", "g"."UpdatedAt"
FROM "GoogleApiSettings" AS "g"
LIMIT 1
2025-07-09 18:31:15.720 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "GoogleApiSettings" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_GoogleApiSettings" PRIMARY KEY AUTOINCREMENT,
                    "ClientId" TEXT NOT NULL DEFAULT '',
                    "ClientSecret" TEXT NOT NULL DEFAULT '',
                    "AccessToken" TEXT NOT NULL DEFAULT '',
                    "RefreshToken" TEXT NOT NULL DEFAULT '',
                    "TokenExpiresAt" TEXT,
                    "Scopes" TEXT NOT NULL DEFAULT '',
                    "ContactsSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "CalendarSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "ContactsSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "CalendarSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "LastContactsSync" TEXT,
                    "LastCalendarSync" TEXT,
                    "ContactsSyncToken" TEXT NOT NULL DEFAULT '',
                    "CalendarSyncToken" TEXT NOT NULL DEFAULT '',
                    "IsAuthenticated" INTEGER NOT NULL DEFAULT 0,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now'))
                );
2025-07-09 18:31:15.721 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "CalendarEvents" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_CalendarEvents" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "CalendarId" TEXT NOT NULL DEFAULT '',
                    "Summary" TEXT NOT NULL DEFAULT '',
                    "Description" TEXT NOT NULL DEFAULT '',
                    "Location" TEXT NOT NULL DEFAULT '',
                    "StartDateTime" TEXT,
                    "EndDateTime" TEXT,
                    "IsAllDay" INTEGER NOT NULL DEFAULT 0,
                    "TimeZone" TEXT NOT NULL DEFAULT '',
                    "Status" TEXT NOT NULL DEFAULT '',
                    "Visibility" TEXT NOT NULL DEFAULT '',
                    "Attendees" TEXT NOT NULL DEFAULT '',
                    "Organizer" TEXT NOT NULL DEFAULT '',
                    "Creator" TEXT NOT NULL DEFAULT '',
                    "RecurrenceRule" TEXT NOT NULL DEFAULT '',
                    "RecurringEventId" TEXT NOT NULL DEFAULT '',
                    "HtmlLink" TEXT NOT NULL DEFAULT '',
                    "HangoutLink" TEXT NOT NULL DEFAULT '',
                    "ConferenceData" TEXT NOT NULL DEFAULT '',
                    "Reminders" TEXT NOT NULL DEFAULT '',
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:31:15.721 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "Contacts" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_Contacts" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "DisplayName" TEXT NOT NULL DEFAULT '',
                    "GivenName" TEXT NOT NULL DEFAULT '',
                    "FamilyName" TEXT NOT NULL DEFAULT '',
                    "MiddleName" TEXT NOT NULL DEFAULT '',
                    "EmailAddresses" TEXT NOT NULL DEFAULT '',
                    "PhoneNumbers" TEXT NOT NULL DEFAULT '',
                    "Addresses" TEXT NOT NULL DEFAULT '',
                    "Organizations" TEXT NOT NULL DEFAULT '',
                    "PhotoUrl" TEXT NOT NULL DEFAULT '',
                    "Notes" TEXT NOT NULL DEFAULT '',
                    "Birthday" TEXT,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:31:15.721 -07:00 [INF] Google API tables ensured to exist
2025-07-09 18:31:15.721 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "g"."Id", "g"."AccessToken", "g"."CalendarSyncEnabled", "g"."CalendarSyncIntervalMinutes", "g"."CalendarSyncToken", "g"."ClientId", "g"."ClientSecret", "g"."ContactsSyncEnabled", "g"."ContactsSyncIntervalMinutes", "g"."ContactsSyncToken", "g"."CreatedAt", "g"."IsAuthenticated", "g"."LastCalendarSync", "g"."LastContactsSync", "g"."RefreshToken", "g"."Scopes", "g"."TokenExpiresAt", "g"."UpdatedAt"
FROM "GoogleApiSettings" AS "g"
LIMIT 1
2025-07-09 18:31:15.721 -07:00 [WRN] No Google API settings found
2025-07-09 18:31:34.235 -07:00 [INF] OAuth flow creation successful - credentials appear valid
2025-07-09 18:31:36.557 -07:00 [INF] Starting Google authentication with Client ID: ************-hsi6qdu8efet4ha6eapbn6oqnl0hqna5.apps.googleusercontent.com
2025-07-09 18:31:36.558 -07:00 [INF] Starting OAuth flow with scopes: https://www.googleapis.com/auth/contacts.readonly, https://www.googleapis.com/auth/calendar.readonly, https://www.googleapis.com/auth/userinfo.profile, https://www.googleapis.com/auth/userinfo.email
2025-07-09 18:31:36.568 -07:00 [INF] Using OAuth redirect port: 8080
2025-07-09 18:31:36.569 -07:00 [INF] OAuth redirect URI will be: http://127.0.0.1:8080/authorize/
2025-07-09 18:31:36.569 -07:00 [INF] Starting OAuth authorization flow...
2025-07-09 18:31:36.571 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "GoogleApiSettings" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_GoogleApiSettings" PRIMARY KEY AUTOINCREMENT,
                    "ClientId" TEXT NOT NULL DEFAULT '',
                    "ClientSecret" TEXT NOT NULL DEFAULT '',
                    "AccessToken" TEXT NOT NULL DEFAULT '',
                    "RefreshToken" TEXT NOT NULL DEFAULT '',
                    "TokenExpiresAt" TEXT,
                    "Scopes" TEXT NOT NULL DEFAULT '',
                    "ContactsSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "CalendarSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "ContactsSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "CalendarSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "LastContactsSync" TEXT,
                    "LastCalendarSync" TEXT,
                    "ContactsSyncToken" TEXT NOT NULL DEFAULT '',
                    "CalendarSyncToken" TEXT NOT NULL DEFAULT '',
                    "IsAuthenticated" INTEGER NOT NULL DEFAULT 0,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now'))
                );
2025-07-09 18:31:36.571 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "g"."Id", "g"."AccessToken", "g"."CalendarSyncEnabled", "g"."CalendarSyncIntervalMinutes", "g"."CalendarSyncToken", "g"."ClientId", "g"."ClientSecret", "g"."ContactsSyncEnabled", "g"."ContactsSyncIntervalMinutes", "g"."ContactsSyncToken", "g"."CreatedAt", "g"."IsAuthenticated", "g"."LastCalendarSync", "g"."LastContactsSync", "g"."RefreshToken", "g"."Scopes", "g"."TokenExpiresAt", "g"."UpdatedAt"
FROM "GoogleApiSettings" AS "g"
LIMIT 1
2025-07-09 18:31:36.579 -07:00 [INF] Starting OAuth callback listener on 127.0.0.1:8080
2025-07-09 18:31:36.579 -07:00 [INF] Authorization URL: https://accounts.google.com/o/oauth2/v2/auth?access_type=offline&response_type=code&client_id=************-hsi6qdu8efet4ha6eapbn6oqnl0hqna5.apps.googleusercontent.com&redirect_uri=http%3A%2F%2F127.0.0.1%3A8080%2Fauthorize%2F&scope=https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcontacts.readonly https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcalendar.readonly https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email
2025-07-09 18:31:36.580 -07:00 [INF] HTTP listener started successfully on port 8080
2025-07-09 18:31:36.580 -07:00 [INF] Opening browser to: https://accounts.google.com/o/oauth2/v2/auth?access_type=offline&response_type=code&client_id=************-hsi6qdu8efet4ha6eapbn6oqnl0hqna5.apps.googleusercontent.com&redirect_uri=http%3A%2F%2F127.0.0.1%3A8080%2Fauthorize%2F&scope=https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcontacts.readonly https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcalendar.readonly https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email
2025-07-09 18:31:36.619 -07:00 [INF] Browser opened successfully
2025-07-09 18:31:36.619 -07:00 [INF] Waiting for OAuth callback...
2025-07-09 18:31:46.324 -07:00 [INF] Received OAuth callback from 127.0.0.1:8853
2025-07-09 18:31:46.325 -07:00 [INF] Request URL: "http://127.0.0.1:8080/authorize/?code=4/0AVMBsJgpLTWjxN0YGTN3jnaf0BDSUPfyirwbof6JBxUldHCHtm7Ym19f2ftUNgzmQMfO2A&scope=email profile https://www.googleapis.com/auth/contacts.readonly https://www.googleapis.com/auth/calendar.readonly https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email openid&authuser=0&prompt=consent"
2025-07-09 18:31:46.325 -07:00 [INF] Query string: ?code=4/0AVMBsJgpLTWjxN0YGTN3jnaf0BDSUPfyirwbof6JBxUldHCHtm7Ym19f2ftUNgzmQMfO2A&scope=email%20profile%20https://www.googleapis.com/auth/contacts.readonly%20https://www.googleapis.com/auth/calendar.readonly%20https://www.googleapis.com/auth/userinfo.profile%20https://www.googleapis.com/auth/userinfo.email%20openid&authuser=0&prompt=consent
2025-07-09 18:31:46.327 -07:00 [INF] Creating AuthorizationCodeResponseUrl with query: ?code=4/0AVMBsJgpLTWjxN0YGTN3jnaf0BDSUPfyirwbof6JBxUldHCHtm7Ym19f2ftUNgzmQMfO2A&scope=email%20profile%20https://www.googleapis.com/auth/contacts.readonly%20https://www.googleapis.com/auth/calendar.readonly%20https://www.googleapis.com/auth/userinfo.profile%20https://www.googleapis.com/auth/userinfo.email%20openid&authuser=0&prompt=consent
2025-07-09 18:31:46.328 -07:00 [INF] Parsed authorization code: null
2025-07-09 18:31:46.328 -07:00 [INF] Parsed state: null
2025-07-09 18:31:46.328 -07:00 [INF] Parsed error: null
2025-07-09 18:31:46.328 -07:00 [INF] HTTP listener stopped
2025-07-09 18:31:46.329 -07:00 [ERR] OAuth token error: null - null
Google.Apis.Auth.OAuth2.Responses.TokenResponseException: Error:"", Description:"", Uri:""
   at Google.Apis.Auth.OAuth2.AuthorizationCodeInstalledApp.AuthorizeAsync(String userId, CancellationToken taskCancellationToken)
   at EmailClient.Services.GoogleAuthService.AuthenticateAsync(String clientId, String clientSecret, String[] scopes) in C:\Users\<USER>\dev\windows app\EmailClient\Services\GoogleAuthService.cs:line 75
2025-07-09 18:31:46.338 -07:00 [ERR] Failed to authenticate with Google: OAuth authentication failed:  - 
System.InvalidOperationException: OAuth authentication failed:  - 
 ---> Google.Apis.Auth.OAuth2.Responses.TokenResponseException: Error:"", Description:"", Uri:""
   at Google.Apis.Auth.OAuth2.AuthorizationCodeInstalledApp.AuthorizeAsync(String userId, CancellationToken taskCancellationToken)
   at EmailClient.Services.GoogleAuthService.AuthenticateAsync(String clientId, String clientSecret, String[] scopes) in C:\Users\<USER>\dev\windows app\EmailClient\Services\GoogleAuthService.cs:line 75
   --- End of inner exception stack trace ---
   at EmailClient.Services.GoogleAuthService.AuthenticateAsync(String clientId, String clientSecret, String[] scopes) in C:\Users\<USER>\dev\windows app\EmailClient\Services\GoogleAuthService.cs:line 98
2025-07-09 18:31:50.445 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "GoogleApiSettings" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_GoogleApiSettings" PRIMARY KEY AUTOINCREMENT,
                    "ClientId" TEXT NOT NULL DEFAULT '',
                    "ClientSecret" TEXT NOT NULL DEFAULT '',
                    "AccessToken" TEXT NOT NULL DEFAULT '',
                    "RefreshToken" TEXT NOT NULL DEFAULT '',
                    "TokenExpiresAt" TEXT,
                    "Scopes" TEXT NOT NULL DEFAULT '',
                    "ContactsSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "CalendarSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "ContactsSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "CalendarSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "LastContactsSync" TEXT,
                    "LastCalendarSync" TEXT,
                    "ContactsSyncToken" TEXT NOT NULL DEFAULT '',
                    "CalendarSyncToken" TEXT NOT NULL DEFAULT '',
                    "IsAuthenticated" INTEGER NOT NULL DEFAULT 0,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now'))
                );
2025-07-09 18:31:50.445 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "CalendarEvents" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_CalendarEvents" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "CalendarId" TEXT NOT NULL DEFAULT '',
                    "Summary" TEXT NOT NULL DEFAULT '',
                    "Description" TEXT NOT NULL DEFAULT '',
                    "Location" TEXT NOT NULL DEFAULT '',
                    "StartDateTime" TEXT,
                    "EndDateTime" TEXT,
                    "IsAllDay" INTEGER NOT NULL DEFAULT 0,
                    "TimeZone" TEXT NOT NULL DEFAULT '',
                    "Status" TEXT NOT NULL DEFAULT '',
                    "Visibility" TEXT NOT NULL DEFAULT '',
                    "Attendees" TEXT NOT NULL DEFAULT '',
                    "Organizer" TEXT NOT NULL DEFAULT '',
                    "Creator" TEXT NOT NULL DEFAULT '',
                    "RecurrenceRule" TEXT NOT NULL DEFAULT '',
                    "RecurringEventId" TEXT NOT NULL DEFAULT '',
                    "HtmlLink" TEXT NOT NULL DEFAULT '',
                    "HangoutLink" TEXT NOT NULL DEFAULT '',
                    "ConferenceData" TEXT NOT NULL DEFAULT '',
                    "Reminders" TEXT NOT NULL DEFAULT '',
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:31:50.445 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "Contacts" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_Contacts" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "DisplayName" TEXT NOT NULL DEFAULT '',
                    "GivenName" TEXT NOT NULL DEFAULT '',
                    "FamilyName" TEXT NOT NULL DEFAULT '',
                    "MiddleName" TEXT NOT NULL DEFAULT '',
                    "EmailAddresses" TEXT NOT NULL DEFAULT '',
                    "PhoneNumbers" TEXT NOT NULL DEFAULT '',
                    "Addresses" TEXT NOT NULL DEFAULT '',
                    "Organizations" TEXT NOT NULL DEFAULT '',
                    "PhotoUrl" TEXT NOT NULL DEFAULT '',
                    "Notes" TEXT NOT NULL DEFAULT '',
                    "Birthday" TEXT,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:31:50.445 -07:00 [INF] Google API tables ensured to exist
2025-07-09 18:31:50.445 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "g"."Id", "g"."AccessToken", "g"."CalendarSyncEnabled", "g"."CalendarSyncIntervalMinutes", "g"."CalendarSyncToken", "g"."ClientId", "g"."ClientSecret", "g"."ContactsSyncEnabled", "g"."ContactsSyncIntervalMinutes", "g"."ContactsSyncToken", "g"."CreatedAt", "g"."IsAuthenticated", "g"."LastCalendarSync", "g"."LastContactsSync", "g"."RefreshToken", "g"."Scopes", "g"."TokenExpiresAt", "g"."UpdatedAt"
FROM "GoogleApiSettings" AS "g"
LIMIT 1
2025-07-09 18:31:50.446 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "GoogleApiSettings" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_GoogleApiSettings" PRIMARY KEY AUTOINCREMENT,
                    "ClientId" TEXT NOT NULL DEFAULT '',
                    "ClientSecret" TEXT NOT NULL DEFAULT '',
                    "AccessToken" TEXT NOT NULL DEFAULT '',
                    "RefreshToken" TEXT NOT NULL DEFAULT '',
                    "TokenExpiresAt" TEXT,
                    "Scopes" TEXT NOT NULL DEFAULT '',
                    "ContactsSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "CalendarSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "ContactsSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "CalendarSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "LastContactsSync" TEXT,
                    "LastCalendarSync" TEXT,
                    "ContactsSyncToken" TEXT NOT NULL DEFAULT '',
                    "CalendarSyncToken" TEXT NOT NULL DEFAULT '',
                    "IsAuthenticated" INTEGER NOT NULL DEFAULT 0,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now'))
                );
2025-07-09 18:31:50.446 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "CalendarEvents" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_CalendarEvents" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "CalendarId" TEXT NOT NULL DEFAULT '',
                    "Summary" TEXT NOT NULL DEFAULT '',
                    "Description" TEXT NOT NULL DEFAULT '',
                    "Location" TEXT NOT NULL DEFAULT '',
                    "StartDateTime" TEXT,
                    "EndDateTime" TEXT,
                    "IsAllDay" INTEGER NOT NULL DEFAULT 0,
                    "TimeZone" TEXT NOT NULL DEFAULT '',
                    "Status" TEXT NOT NULL DEFAULT '',
                    "Visibility" TEXT NOT NULL DEFAULT '',
                    "Attendees" TEXT NOT NULL DEFAULT '',
                    "Organizer" TEXT NOT NULL DEFAULT '',
                    "Creator" TEXT NOT NULL DEFAULT '',
                    "RecurrenceRule" TEXT NOT NULL DEFAULT '',
                    "RecurringEventId" TEXT NOT NULL DEFAULT '',
                    "HtmlLink" TEXT NOT NULL DEFAULT '',
                    "HangoutLink" TEXT NOT NULL DEFAULT '',
                    "ConferenceData" TEXT NOT NULL DEFAULT '',
                    "Reminders" TEXT NOT NULL DEFAULT '',
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:31:50.447 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "Contacts" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_Contacts" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "DisplayName" TEXT NOT NULL DEFAULT '',
                    "GivenName" TEXT NOT NULL DEFAULT '',
                    "FamilyName" TEXT NOT NULL DEFAULT '',
                    "MiddleName" TEXT NOT NULL DEFAULT '',
                    "EmailAddresses" TEXT NOT NULL DEFAULT '',
                    "PhoneNumbers" TEXT NOT NULL DEFAULT '',
                    "Addresses" TEXT NOT NULL DEFAULT '',
                    "Organizations" TEXT NOT NULL DEFAULT '',
                    "PhotoUrl" TEXT NOT NULL DEFAULT '',
                    "Notes" TEXT NOT NULL DEFAULT '',
                    "Birthday" TEXT,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:31:50.447 -07:00 [INF] Google API tables ensured to exist
2025-07-09 18:31:50.447 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "g"."Id", "g"."AccessToken", "g"."CalendarSyncEnabled", "g"."CalendarSyncIntervalMinutes", "g"."CalendarSyncToken", "g"."ClientId", "g"."ClientSecret", "g"."ContactsSyncEnabled", "g"."ContactsSyncIntervalMinutes", "g"."ContactsSyncToken", "g"."CreatedAt", "g"."IsAuthenticated", "g"."LastCalendarSync", "g"."LastContactsSync", "g"."RefreshToken", "g"."Scopes", "g"."TokenExpiresAt", "g"."UpdatedAt"
FROM "GoogleApiSettings" AS "g"
LIMIT 1
2025-07-09 18:31:50.540 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[@p0='?', @p1='?' (DbType = Boolean), @p2='?' (DbType = Int32), @p3='?', @p4='?' (Size = 72), @p5='?' (Size = 35), @p6='?' (DbType = Boolean), @p7='?' (DbType = Int32), @p8='?', @p9='?' (DbType = DateTime), @p10='?' (DbType = Boolean), @p11='?' (DbType = DateTime), @p12='?' (DbType = DateTime), @p13='?', @p14='?', @p15='?' (DbType = DateTime), @p16='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "GoogleApiSettings" ("AccessToken", "CalendarSyncEnabled", "CalendarSyncIntervalMinutes", "CalendarSyncToken", "ClientId", "ClientSecret", "ContactsSyncEnabled", "ContactsSyncIntervalMinutes", "ContactsSyncToken", "CreatedAt", "IsAuthenticated", "LastCalendarSync", "LastContactsSync", "RefreshToken", "Scopes", "TokenExpiresAt", "UpdatedAt")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING "Id";
2025-07-09 18:32:01.967 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "c"."Id", "c"."Addresses", "c"."Birthday", "c"."CreatedAt", "c"."DisplayName", "c"."ETag", "c"."EmailAddresses", "c"."FamilyName", "c"."GivenName", "c"."GoogleId", "c"."IsDeleted", "c"."LastSyncAt", "c"."MiddleName", "c"."Notes", "c"."Organizations", "c"."PhoneNumbers", "c"."PhotoUrl", "c"."UpdatedAt"
FROM "Contacts" AS "c"
WHERE NOT ("c"."IsDeleted")
ORDER BY "c"."DisplayName"
2025-07-09 18:32:03.500 -07:00 [WRN] Google API settings indicate not authenticated (IsAuthenticated = false)
2025-07-09 18:32:03.500 -07:00 [INF] Contacts sync is disabled
2025-07-09 18:32:03.500 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "c"."Id", "c"."Addresses", "c"."Birthday", "c"."CreatedAt", "c"."DisplayName", "c"."ETag", "c"."EmailAddresses", "c"."FamilyName", "c"."GivenName", "c"."GoogleId", "c"."IsDeleted", "c"."LastSyncAt", "c"."MiddleName", "c"."Notes", "c"."Organizations", "c"."PhoneNumbers", "c"."PhotoUrl", "c"."UpdatedAt"
FROM "Contacts" AS "c"
WHERE NOT ("c"."IsDeleted")
ORDER BY "c"."DisplayName"
2025-07-09 18:32:07.017 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "GoogleApiSettings" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_GoogleApiSettings" PRIMARY KEY AUTOINCREMENT,
                    "ClientId" TEXT NOT NULL DEFAULT '',
                    "ClientSecret" TEXT NOT NULL DEFAULT '',
                    "AccessToken" TEXT NOT NULL DEFAULT '',
                    "RefreshToken" TEXT NOT NULL DEFAULT '',
                    "TokenExpiresAt" TEXT,
                    "Scopes" TEXT NOT NULL DEFAULT '',
                    "ContactsSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "CalendarSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "ContactsSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "CalendarSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "LastContactsSync" TEXT,
                    "LastCalendarSync" TEXT,
                    "ContactsSyncToken" TEXT NOT NULL DEFAULT '',
                    "CalendarSyncToken" TEXT NOT NULL DEFAULT '',
                    "IsAuthenticated" INTEGER NOT NULL DEFAULT 0,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now'))
                );
2025-07-09 18:32:07.017 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "CalendarEvents" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_CalendarEvents" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "CalendarId" TEXT NOT NULL DEFAULT '',
                    "Summary" TEXT NOT NULL DEFAULT '',
                    "Description" TEXT NOT NULL DEFAULT '',
                    "Location" TEXT NOT NULL DEFAULT '',
                    "StartDateTime" TEXT,
                    "EndDateTime" TEXT,
                    "IsAllDay" INTEGER NOT NULL DEFAULT 0,
                    "TimeZone" TEXT NOT NULL DEFAULT '',
                    "Status" TEXT NOT NULL DEFAULT '',
                    "Visibility" TEXT NOT NULL DEFAULT '',
                    "Attendees" TEXT NOT NULL DEFAULT '',
                    "Organizer" TEXT NOT NULL DEFAULT '',
                    "Creator" TEXT NOT NULL DEFAULT '',
                    "RecurrenceRule" TEXT NOT NULL DEFAULT '',
                    "RecurringEventId" TEXT NOT NULL DEFAULT '',
                    "HtmlLink" TEXT NOT NULL DEFAULT '',
                    "HangoutLink" TEXT NOT NULL DEFAULT '',
                    "ConferenceData" TEXT NOT NULL DEFAULT '',
                    "Reminders" TEXT NOT NULL DEFAULT '',
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:32:07.018 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "Contacts" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_Contacts" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "DisplayName" TEXT NOT NULL DEFAULT '',
                    "GivenName" TEXT NOT NULL DEFAULT '',
                    "FamilyName" TEXT NOT NULL DEFAULT '',
                    "MiddleName" TEXT NOT NULL DEFAULT '',
                    "EmailAddresses" TEXT NOT NULL DEFAULT '',
                    "PhoneNumbers" TEXT NOT NULL DEFAULT '',
                    "Addresses" TEXT NOT NULL DEFAULT '',
                    "Organizations" TEXT NOT NULL DEFAULT '',
                    "PhotoUrl" TEXT NOT NULL DEFAULT '',
                    "Notes" TEXT NOT NULL DEFAULT '',
                    "Birthday" TEXT,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:32:07.018 -07:00 [INF] Google API tables ensured to exist
2025-07-09 18:32:07.018 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "g"."Id", "g"."AccessToken", "g"."CalendarSyncEnabled", "g"."CalendarSyncIntervalMinutes", "g"."CalendarSyncToken", "g"."ClientId", "g"."ClientSecret", "g"."ContactsSyncEnabled", "g"."ContactsSyncIntervalMinutes", "g"."ContactsSyncToken", "g"."CreatedAt", "g"."IsAuthenticated", "g"."LastCalendarSync", "g"."LastContactsSync", "g"."RefreshToken", "g"."Scopes", "g"."TokenExpiresAt", "g"."UpdatedAt"
FROM "GoogleApiSettings" AS "g"
LIMIT 1
2025-07-09 18:32:07.020 -07:00 [WRN] Google API settings indicate not authenticated (IsAuthenticated = false)
2025-07-09 18:32:07.020 -07:00 [INF] Calendar sync is disabled
2025-07-09 18:32:09.230 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__startDate_Value_0='?' (DbType = DateTime), @__startDate_Value_Date_1='?' (DbType = DateTime), @__endDate_Value_2='?' (DbType = DateTime), @__endDate_Value_Date_3='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "c"."Id", "c"."Attendees", "c"."CalendarId", "c"."ConferenceData", "c"."CreatedAt", "c"."Creator", "c"."Description", "c"."ETag", "c"."EndDateTime", "c"."GoogleId", "c"."HangoutLink", "c"."HtmlLink", "c"."IsAllDay", "c"."IsDeleted", "c"."LastSyncAt", "c"."Location", "c"."Organizer", "c"."RecurrenceRule", "c"."RecurringEventId", "c"."Reminders", "c"."StartDateTime", "c"."Status", "c"."Summary", "c"."TimeZone", "c"."UpdatedAt", "c"."Visibility"
FROM "CalendarEvents" AS "c"
WHERE NOT ("c"."IsDeleted") AND ("c"."EndDateTime" >= @__startDate_Value_0 OR ("c"."IsAllDay" AND "c"."StartDateTime" >= @__startDate_Value_Date_1)) AND ("c"."StartDateTime" <= @__endDate_Value_2 OR ("c"."IsAllDay" AND "c"."StartDateTime" <= @__endDate_Value_Date_3))
ORDER BY "c"."StartDateTime"
2025-07-09 18:32:09.233 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__now_0='?' (DbType = DateTime), @__endDate_1='?' (DbType = DateTime), @__p_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "c"."Id", "c"."Attendees", "c"."CalendarId", "c"."ConferenceData", "c"."CreatedAt", "c"."Creator", "c"."Description", "c"."ETag", "c"."EndDateTime", "c"."GoogleId", "c"."HangoutLink", "c"."HtmlLink", "c"."IsAllDay", "c"."IsDeleted", "c"."LastSyncAt", "c"."Location", "c"."Organizer", "c"."RecurrenceRule", "c"."RecurringEventId", "c"."Reminders", "c"."StartDateTime", "c"."Status", "c"."Summary", "c"."TimeZone", "c"."UpdatedAt", "c"."Visibility"
FROM "CalendarEvents" AS "c"
WHERE NOT ("c"."IsDeleted") AND "c"."StartDateTime" >= @__now_0 AND "c"."StartDateTime" <= @__endDate_1
ORDER BY "c"."StartDateTime"
LIMIT @__p_2
2025-07-09 18:32:09.240 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__startOfDay_0='?' (DbType = DateTime), @__endOfDay_1='?' (DbType = DateTime), @__date_Date_2='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "c"."Id", "c"."Attendees", "c"."CalendarId", "c"."ConferenceData", "c"."CreatedAt", "c"."Creator", "c"."Description", "c"."ETag", "c"."EndDateTime", "c"."GoogleId", "c"."HangoutLink", "c"."HtmlLink", "c"."IsAllDay", "c"."IsDeleted", "c"."LastSyncAt", "c"."Location", "c"."Organizer", "c"."RecurrenceRule", "c"."RecurringEventId", "c"."Reminders", "c"."StartDateTime", "c"."Status", "c"."Summary", "c"."TimeZone", "c"."UpdatedAt", "c"."Visibility"
FROM "CalendarEvents" AS "c"
WHERE NOT ("c"."IsDeleted") AND (("c"."StartDateTime" >= @__startOfDay_0 AND "c"."StartDateTime" < @__endOfDay_1) OR ("c"."EndDateTime" > @__startOfDay_0 AND "c"."EndDateTime" <= @__endOfDay_1) OR ("c"."StartDateTime" < @__startOfDay_0 AND "c"."EndDateTime" > @__endOfDay_1) OR ("c"."IsAllDay" AND "c"."StartDateTime" IS NOT NULL AND rtrim(rtrim(strftime('%Y-%m-%d %H:%M:%f', "c"."StartDateTime", 'start of day'), '0'), '.') = @__date_Date_2))
ORDER BY "c"."StartDateTime"
2025-07-09 18:32:11.004 -07:00 [WRN] Google API settings indicate not authenticated (IsAuthenticated = false)
2025-07-09 18:32:11.004 -07:00 [INF] Calendar sync is disabled
2025-07-09 18:32:11.004 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__startDate_Value_0='?' (DbType = DateTime), @__startDate_Value_Date_1='?' (DbType = DateTime), @__endDate_Value_2='?' (DbType = DateTime), @__endDate_Value_Date_3='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "c"."Id", "c"."Attendees", "c"."CalendarId", "c"."ConferenceData", "c"."CreatedAt", "c"."Creator", "c"."Description", "c"."ETag", "c"."EndDateTime", "c"."GoogleId", "c"."HangoutLink", "c"."HtmlLink", "c"."IsAllDay", "c"."IsDeleted", "c"."LastSyncAt", "c"."Location", "c"."Organizer", "c"."RecurrenceRule", "c"."RecurringEventId", "c"."Reminders", "c"."StartDateTime", "c"."Status", "c"."Summary", "c"."TimeZone", "c"."UpdatedAt", "c"."Visibility"
FROM "CalendarEvents" AS "c"
WHERE NOT ("c"."IsDeleted") AND ("c"."EndDateTime" >= @__startDate_Value_0 OR ("c"."IsAllDay" AND "c"."StartDateTime" >= @__startDate_Value_Date_1)) AND ("c"."StartDateTime" <= @__endDate_Value_2 OR ("c"."IsAllDay" AND "c"."StartDateTime" <= @__endDate_Value_Date_3))
ORDER BY "c"."StartDateTime"
2025-07-09 18:32:11.005 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__now_0='?' (DbType = DateTime), @__endDate_1='?' (DbType = DateTime), @__p_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "c"."Id", "c"."Attendees", "c"."CalendarId", "c"."ConferenceData", "c"."CreatedAt", "c"."Creator", "c"."Description", "c"."ETag", "c"."EndDateTime", "c"."GoogleId", "c"."HangoutLink", "c"."HtmlLink", "c"."IsAllDay", "c"."IsDeleted", "c"."LastSyncAt", "c"."Location", "c"."Organizer", "c"."RecurrenceRule", "c"."RecurringEventId", "c"."Reminders", "c"."StartDateTime", "c"."Status", "c"."Summary", "c"."TimeZone", "c"."UpdatedAt", "c"."Visibility"
FROM "CalendarEvents" AS "c"
WHERE NOT ("c"."IsDeleted") AND "c"."StartDateTime" >= @__now_0 AND "c"."StartDateTime" <= @__endDate_1
ORDER BY "c"."StartDateTime"
LIMIT @__p_2
2025-07-09 18:32:11.005 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__startOfDay_0='?' (DbType = DateTime), @__endOfDay_1='?' (DbType = DateTime), @__date_Date_2='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "c"."Id", "c"."Attendees", "c"."CalendarId", "c"."ConferenceData", "c"."CreatedAt", "c"."Creator", "c"."Description", "c"."ETag", "c"."EndDateTime", "c"."GoogleId", "c"."HangoutLink", "c"."HtmlLink", "c"."IsAllDay", "c"."IsDeleted", "c"."LastSyncAt", "c"."Location", "c"."Organizer", "c"."RecurrenceRule", "c"."RecurringEventId", "c"."Reminders", "c"."StartDateTime", "c"."Status", "c"."Summary", "c"."TimeZone", "c"."UpdatedAt", "c"."Visibility"
FROM "CalendarEvents" AS "c"
WHERE NOT ("c"."IsDeleted") AND (("c"."StartDateTime" >= @__startOfDay_0 AND "c"."StartDateTime" < @__endOfDay_1) OR ("c"."EndDateTime" > @__startOfDay_0 AND "c"."EndDateTime" <= @__endOfDay_1) OR ("c"."StartDateTime" < @__startOfDay_0 AND "c"."EndDateTime" > @__endOfDay_1) OR ("c"."IsAllDay" AND "c"."StartDateTime" IS NOT NULL AND rtrim(rtrim(strftime('%Y-%m-%d %H:%M:%f', "c"."StartDateTime", 'start of day'), '0'), '.') = @__date_Date_2))
ORDER BY "c"."StartDateTime"
2025-07-09 18:32:13.670 -07:00 [INF] Starting background sync
2025-07-09 18:32:13.671 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
WHERE "a"."IsEnabled"
2025-07-09 18:32:13.671 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "GoogleApiSettings" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_GoogleApiSettings" PRIMARY KEY AUTOINCREMENT,
                    "ClientId" TEXT NOT NULL DEFAULT '',
                    "ClientSecret" TEXT NOT NULL DEFAULT '',
                    "AccessToken" TEXT NOT NULL DEFAULT '',
                    "RefreshToken" TEXT NOT NULL DEFAULT '',
                    "TokenExpiresAt" TEXT,
                    "Scopes" TEXT NOT NULL DEFAULT '',
                    "ContactsSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "CalendarSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "ContactsSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "CalendarSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "LastContactsSync" TEXT,
                    "LastCalendarSync" TEXT,
                    "ContactsSyncToken" TEXT NOT NULL DEFAULT '',
                    "CalendarSyncToken" TEXT NOT NULL DEFAULT '',
                    "IsAuthenticated" INTEGER NOT NULL DEFAULT 0,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now'))
                );
2025-07-09 18:32:13.671 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "CalendarEvents" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_CalendarEvents" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "CalendarId" TEXT NOT NULL DEFAULT '',
                    "Summary" TEXT NOT NULL DEFAULT '',
                    "Description" TEXT NOT NULL DEFAULT '',
                    "Location" TEXT NOT NULL DEFAULT '',
                    "StartDateTime" TEXT,
                    "EndDateTime" TEXT,
                    "IsAllDay" INTEGER NOT NULL DEFAULT 0,
                    "TimeZone" TEXT NOT NULL DEFAULT '',
                    "Status" TEXT NOT NULL DEFAULT '',
                    "Visibility" TEXT NOT NULL DEFAULT '',
                    "Attendees" TEXT NOT NULL DEFAULT '',
                    "Organizer" TEXT NOT NULL DEFAULT '',
                    "Creator" TEXT NOT NULL DEFAULT '',
                    "RecurrenceRule" TEXT NOT NULL DEFAULT '',
                    "RecurringEventId" TEXT NOT NULL DEFAULT '',
                    "HtmlLink" TEXT NOT NULL DEFAULT '',
                    "HangoutLink" TEXT NOT NULL DEFAULT '',
                    "ConferenceData" TEXT NOT NULL DEFAULT '',
                    "Reminders" TEXT NOT NULL DEFAULT '',
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:32:13.671 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "Contacts" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_Contacts" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "DisplayName" TEXT NOT NULL DEFAULT '',
                    "GivenName" TEXT NOT NULL DEFAULT '',
                    "FamilyName" TEXT NOT NULL DEFAULT '',
                    "MiddleName" TEXT NOT NULL DEFAULT '',
                    "EmailAddresses" TEXT NOT NULL DEFAULT '',
                    "PhoneNumbers" TEXT NOT NULL DEFAULT '',
                    "Addresses" TEXT NOT NULL DEFAULT '',
                    "Organizations" TEXT NOT NULL DEFAULT '',
                    "PhotoUrl" TEXT NOT NULL DEFAULT '',
                    "Notes" TEXT NOT NULL DEFAULT '',
                    "Birthday" TEXT,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:32:13.671 -07:00 [INF] Google API tables ensured to exist
2025-07-09 18:32:13.671 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "g"."Id", "g"."AccessToken", "g"."CalendarSyncEnabled", "g"."CalendarSyncIntervalMinutes", "g"."CalendarSyncToken", "g"."ClientId", "g"."ClientSecret", "g"."ContactsSyncEnabled", "g"."ContactsSyncIntervalMinutes", "g"."ContactsSyncToken", "g"."CreatedAt", "g"."IsAuthenticated", "g"."LastCalendarSync", "g"."LastContactsSync", "g"."RefreshToken", "g"."Scopes", "g"."TokenExpiresAt", "g"."UpdatedAt"
FROM "GoogleApiSettings" AS "g"
LIMIT 1
2025-07-09 18:32:13.671 -07:00 [WRN] Google API settings indicate not authenticated (IsAuthenticated = false)
2025-07-09 18:32:13.671 -07:00 [INF] Background sync completed successfully
2025-07-09 18:32:13.672 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
ORDER BY "a"."Name"
2025-07-09 18:32:16.291 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."AccountId", "t"."BccAddresses", "t"."CcAddresses", "t"."DateReceived", "t"."DateSent", "t"."FolderId", "t"."FromAddress", "t"."FromName", "t"."HasAttachments", "t"."HtmlBody", "t"."IsDeleted", "t"."IsFlagged", "t"."IsRead", "t"."MessageId", "t"."Size", "t"."Subject", "t"."TextBody", "t"."ToAddresses", "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username", "t"."Id0", "t"."AccountId0", "t"."FullName", "t"."LastSyncAt", "t"."Name", "t"."ParentFolderId", "t"."TotalCount", "t"."Type", "t"."UnreadCount"
FROM (
    SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id" AS "Id0", "f"."AccountId" AS "AccountId0", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
    FROM "Messages" AS "m"
    LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
    WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
    ORDER BY "m"."DateReceived" DESC
    LIMIT @__p_0
) AS "t"
INNER JOIN "Accounts" AS "a" ON "t"."AccountId" = "a"."Id"
ORDER BY "t"."DateReceived" DESC
2025-07-09 18:32:21.898 -07:00 [INF] Starting background sync
2025-07-09 18:32:21.900 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
WHERE "a"."IsEnabled"
2025-07-09 18:32:21.901 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "GoogleApiSettings" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_GoogleApiSettings" PRIMARY KEY AUTOINCREMENT,
                    "ClientId" TEXT NOT NULL DEFAULT '',
                    "ClientSecret" TEXT NOT NULL DEFAULT '',
                    "AccessToken" TEXT NOT NULL DEFAULT '',
                    "RefreshToken" TEXT NOT NULL DEFAULT '',
                    "TokenExpiresAt" TEXT,
                    "Scopes" TEXT NOT NULL DEFAULT '',
                    "ContactsSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "CalendarSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "ContactsSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "CalendarSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "LastContactsSync" TEXT,
                    "LastCalendarSync" TEXT,
                    "ContactsSyncToken" TEXT NOT NULL DEFAULT '',
                    "CalendarSyncToken" TEXT NOT NULL DEFAULT '',
                    "IsAuthenticated" INTEGER NOT NULL DEFAULT 0,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now'))
                );
2025-07-09 18:32:21.901 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "CalendarEvents" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_CalendarEvents" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "CalendarId" TEXT NOT NULL DEFAULT '',
                    "Summary" TEXT NOT NULL DEFAULT '',
                    "Description" TEXT NOT NULL DEFAULT '',
                    "Location" TEXT NOT NULL DEFAULT '',
                    "StartDateTime" TEXT,
                    "EndDateTime" TEXT,
                    "IsAllDay" INTEGER NOT NULL DEFAULT 0,
                    "TimeZone" TEXT NOT NULL DEFAULT '',
                    "Status" TEXT NOT NULL DEFAULT '',
                    "Visibility" TEXT NOT NULL DEFAULT '',
                    "Attendees" TEXT NOT NULL DEFAULT '',
                    "Organizer" TEXT NOT NULL DEFAULT '',
                    "Creator" TEXT NOT NULL DEFAULT '',
                    "RecurrenceRule" TEXT NOT NULL DEFAULT '',
                    "RecurringEventId" TEXT NOT NULL DEFAULT '',
                    "HtmlLink" TEXT NOT NULL DEFAULT '',
                    "HangoutLink" TEXT NOT NULL DEFAULT '',
                    "ConferenceData" TEXT NOT NULL DEFAULT '',
                    "Reminders" TEXT NOT NULL DEFAULT '',
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:32:21.901 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "Contacts" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_Contacts" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "DisplayName" TEXT NOT NULL DEFAULT '',
                    "GivenName" TEXT NOT NULL DEFAULT '',
                    "FamilyName" TEXT NOT NULL DEFAULT '',
                    "MiddleName" TEXT NOT NULL DEFAULT '',
                    "EmailAddresses" TEXT NOT NULL DEFAULT '',
                    "PhoneNumbers" TEXT NOT NULL DEFAULT '',
                    "Addresses" TEXT NOT NULL DEFAULT '',
                    "Organizations" TEXT NOT NULL DEFAULT '',
                    "PhotoUrl" TEXT NOT NULL DEFAULT '',
                    "Notes" TEXT NOT NULL DEFAULT '',
                    "Birthday" TEXT,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:32:21.901 -07:00 [INF] Google API tables ensured to exist
2025-07-09 18:32:21.901 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "g"."Id", "g"."AccessToken", "g"."CalendarSyncEnabled", "g"."CalendarSyncIntervalMinutes", "g"."CalendarSyncToken", "g"."ClientId", "g"."ClientSecret", "g"."ContactsSyncEnabled", "g"."ContactsSyncIntervalMinutes", "g"."ContactsSyncToken", "g"."CreatedAt", "g"."IsAuthenticated", "g"."LastCalendarSync", "g"."LastContactsSync", "g"."RefreshToken", "g"."Scopes", "g"."TokenExpiresAt", "g"."UpdatedAt"
FROM "GoogleApiSettings" AS "g"
LIMIT 1
2025-07-09 18:32:21.901 -07:00 [WRN] Google API settings indicate not authenticated (IsAuthenticated = false)
2025-07-09 18:32:21.901 -07:00 [INF] Background sync completed successfully
2025-07-09 18:32:21.902 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."AccountId", "t"."BccAddresses", "t"."CcAddresses", "t"."DateReceived", "t"."DateSent", "t"."FolderId", "t"."FromAddress", "t"."FromName", "t"."HasAttachments", "t"."HtmlBody", "t"."IsDeleted", "t"."IsFlagged", "t"."IsRead", "t"."MessageId", "t"."Size", "t"."Subject", "t"."TextBody", "t"."ToAddresses", "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username", "t"."Id0", "t"."AccountId0", "t"."FullName", "t"."LastSyncAt", "t"."Name", "t"."ParentFolderId", "t"."TotalCount", "t"."Type", "t"."UnreadCount"
FROM (
    SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id" AS "Id0", "f"."AccountId" AS "AccountId0", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
    FROM "Messages" AS "m"
    LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
    WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
    ORDER BY "m"."DateReceived" DESC
    LIMIT @__p_0
) AS "t"
INNER JOIN "Accounts" AS "a" ON "t"."AccountId" = "a"."Id"
ORDER BY "t"."DateReceived" DESC
2025-07-09 18:32:21.902 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
ORDER BY "a"."Name"
2025-07-09 18:32:30.679 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
2025-07-09 18:33:05.414 -07:00 [INF] Stopping sync service
